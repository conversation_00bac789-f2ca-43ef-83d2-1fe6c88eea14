<template>
  <view class="edit-profile-container">
    <!-- 导航栏 -->
    <sla-navbar
      title="编辑资料"
      @back="navigateBack"
    />

    <!-- 头部区域 -->
    <view class="edit-profile-header">
      <view class="edit-profile-header-bg">
        <!-- 背景装饰 -->
      </view>

      <!-- 头像区域 -->
      <view class="edit-profile-avatar-wrapper">
        <view class="edit-profile-avatar-container">
          <sla-avatar
            :src="formData.avatar"
            size="large"
            :editable="true"
            @change="handleAvatarChange"
            class="edit-profile-avatar"
          />
          <text class="edit-profile-avatar-hint">点击更换</text>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="edit-profile-form-container">
      <!-- 基本信息 -->
      <view class="edit-profile-card">
        <view class="edit-profile-card-header">
          <text class="edit-profile-card-title">📝 基本信息</text>
        </view>

        <view class="edit-profile-card-body">
          <view class="edit-profile-form-item">
            <text class="edit-profile-form-item__label">用户名</text>
            <input
              class="edit-profile-form-item__input"
              v-model="formData.username"
              placeholder="请输入用户名"
              :maxlength="64"
            />
          </view>

          <view class="edit-profile-divider"></view>

          <view class="edit-profile-form-item">
            <text class="edit-profile-form-item__label">性别</text>
            <view class="edit-profile-gender">
              <view
                class="edit-profile-gender__option"
                :class="{ 'edit-profile-gender__option--active': formData.gender === 1 }"
                @click="handleGenderChange(1)"
              >
                <text class="edit-profile-gender__text">男</text>
              </view>
              <view
                class="edit-profile-gender__option"
                :class="{ 'edit-profile-gender__option--active': formData.gender === 2 }"
                @click="handleGenderChange(2)"
              >
                <text class="edit-profile-gender__text">女</text>
              </view>
              <view
                class="edit-profile-gender__option"
                :class="{ 'edit-profile-gender__option--active': formData.gender === 0 }"
                @click="handleGenderChange(0)"
              >
                <text class="edit-profile-gender__text">保密</text>
              </view>
            </view>
          </view>

          <view class="edit-profile-divider"></view>

          <view class="edit-profile-form-item">
            <text class="edit-profile-form-item__label">身份</text>
            <picker
              mode="selector"
              :range="identityOptions"
              :value="formData.identity"
              @change="handleIdentityChange"
            >
              <view class="edit-profile-picker">
                <text class="edit-profile-picker__text">{{ identityOptions[formData.identity] }}</text>
                <text class="edit-profile-picker__arrow">></text>
              </view>
            </picker>
          </view>

          <view class="edit-profile-divider"></view>

          <view class="edit-profile-form-item">
            <text class="edit-profile-form-item__label">生日</text>
            <picker
              mode="date"
              :value="formData.birthday"
              @change="handleBirthdayChange"
            >
              <view class="edit-profile-picker">
                <text class="edit-profile-picker__text">{{ formData.birthday || '请选择生日' }}</text>
                <text class="edit-profile-picker__arrow">></text>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 个性签名 -->
      <view class="edit-profile-card">
        <view class="edit-profile-card-header">
          <text class="edit-profile-card-title">✨ 个性签名</text>
        </view>

        <view class="edit-profile-card-body">
          <view class="edit-profile-signature-container">
            <textarea
              class="edit-profile-signature-input"
              v-model="formData.signature"
              placeholder="写下你的个性签名，让大家更了解你..."
              :maxlength="64"
            />
            <text class="edit-profile-signature-count">{{ signatureLength }}/64</text>
          </view>
        </view>
      </view>

      <!-- 保存按钮 -->
      <view class="edit-profile-submit">
        <view class="edit-profile-submit-btn" @click="handleSave">
          <text class="edit-profile-submit-btn__text">保存</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import SlaAvatar from '../../components/user/SlaAvatar.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import { getUserInfo, updateUserInfo, uploadFile } from '../../utils/api/user.js';

export default {
  name: 'EditProfile',
  components: {
    SlaAvatar,
    SlaNavbar
  },
  data() {
    return {
      // 表单数据
      formData: {
        username: '',
        avatar: '',
        gender: 0,
        identity: 0,
        signature: '',
        birthday: '',
        email: '',
        workGroup: ''
      },
      // 用户手机号
      userPhone: '',
      // 身份选项
      identityOptions: ['初中', '高中', '大学', '研究生', '在职', '其他'],
      // 头像是否已更改
      isAvatarChanged: false,
      // 新头像本地路径
      newAvatarPath: '',
      // 上传后的头像URL
      uploadedAvatarUrl: ''
    };
  },
  computed: {
    // 签名长度计算
    signatureLength() {
      return this.formData.signature ? this.formData.signature.length : 0;
    }
  },
  onLoad() {
    // 获取用户信息
    this.fetchUserInfo();
  },
  methods: {
    // 获取用户信息
    fetchUserInfo() {
      // 先从缓存获取
      const cachedUserInfo = uni.getStorageSync('userInfo');
      if (cachedUserInfo) {
        this.initFormData(cachedUserInfo);
      }

      // 从服务器获取最新信息
      getUserInfo().then(res => {
        if (res.data) {
          this.initFormData(res.data);
          // 更新缓存
          uni.setStorageSync('userInfo', res.data);
        }
      }).catch(err => {
        console.error('获取用户信息失败', err);
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      });
    },

    // 初始化表单数据
    initFormData(userInfo) {
      this.formData = {
        username: userInfo.username || '',
        avatar: userInfo.avatar || '',
        gender: userInfo.gender || 0,
        identity: userInfo.identity || 0,
        signature: userInfo.signature || '',
        birthday: userInfo.birthday || '',
        email: userInfo.email || '',
        workGroup: userInfo.workGroup || ''
      };
      this.userPhone = userInfo.phone || '';
    },

    // 处理头像更改
    handleAvatarChange(filePath) {
      this.isAvatarChanged = true;
      this.newAvatarPath = filePath;
      this.formData.avatar = filePath; // 临时显示
    },

    // 处理性别选择
    handleGenderChange(gender) {
      this.formData.gender = gender;
    },

    // 处理身份选择
    handleIdentityChange(e) {
      this.formData.identity = parseInt(e.detail.value);
    },

    // 处理生日选择
    handleBirthdayChange(e) {
      this.formData.birthday = e.detail.value;
    },

    // 保存资料
    handleSave() {
      // 表单验证
      if (!this.formData.username.trim()) {
        uni.showToast({
          title: '请输入用户名',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '保存中...',
        mask: true
      });

      // 保存用户资料的核心逻辑
      const saveProfile = () => {
        // 构建更新参数
        const updateParams = {
          username: this.formData.username,
          gender: this.formData.gender,
          identity: this.formData.identity,
          signature: this.formData.signature,
          birthday: this.formData.birthday,
          email: this.formData.email,
          workGroup: this.formData.workGroup
        };

        // 如果头像已更改，添加头像URL
        if (this.isAvatarChanged && this.uploadedAvatarUrl) {
          updateParams.avatar = this.uploadedAvatarUrl;
        }

        // 调用更新接口
        updateUserInfo(updateParams)
          .then(res => {
            if (res.code === 200) {
              // 保存成功
              uni.hideLoading();
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              });

              // 更新本地缓存的用户信息
              const userInfo = uni.getStorageSync('userInfo') || {};
              Object.assign(userInfo, updateParams);

              // 确保用户信息中有id字段，兼容前端代码
              if (!userInfo.userId || userInfo.userId === null) {
                // 如果没有userId，使用hashCode生成临时ID
                if (userInfo.username) {
                  // 简单的hashCode算法
                  const hashCode = str => {
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                      const char = str.charCodeAt(i);
                      hash = ((hash << 5) - hash) + char;
                      hash = hash & hash; // Convert to 32bit integer
                    }
                    return Math.abs(hash); // 确保为正数
                  };

                  userInfo.userId = hashCode(userInfo.username);
                  console.log('userId为空，使用hashCode生成临时ID:', userInfo.userId);
                } else {
                  // 如果连用户名都没有，使用时间戳作为ID
                  userInfo.userId = new Date().getTime();
                  console.log('userId为空，使用时间戳生成临时ID:', userInfo.userId);
                }
              }

              // 确保id字段存在，兼容前端
              if (userInfo.id === undefined || userInfo.id === null) {
                userInfo.id = userInfo.userId;
                console.log('确保id字段存在，设置为:', userInfo.id);
              }

              uni.setStorageSync('userInfo', userInfo);

              // 延迟返回上一页
              setTimeout(() => {
                this.navigateBack();
              }, 1000);
            } else {
              // 保存失败
              uni.hideLoading();
              uni.showToast({
                title: res.message || '保存失败',
                icon: 'none'
              });
            }
          })
          .catch(err => {
            // 网络错误
            uni.hideLoading();
            uni.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            });
            console.error('保存用户信息失败:', err);
          });
      };

      if (this.isAvatarChanged && this.newAvatarPath) {
        // 先上传头像
        uploadFile(this.newAvatarPath, 0).then(res => {
          if (res.data) {
            this.uploadedAvatarUrl = res.data;
            saveProfile();
          } else {
            uni.hideLoading();
            uni.showToast({
              title: '头像上传失败',
              icon: 'none'
            });
          }
        }).catch(err => {
          uni.hideLoading();
          uni.showToast({
            title: '头像上传失败',
            icon: 'none'
          });
        });
      } else {
        saveProfile();
      }
    },

    // 返回上一页
    navigateBack() {
      // 返回到个人资料页面，使用switchTab避免splash页面
      uni.switchTab({
        url: '/pages/user/profile'
      });
    }
  }
}
</script>

<style>
.edit-profile-container {
  min-height: 100vh;
  background-color: #f7f9fc;
  position: relative;
  display: flex;
  flex-direction: column;
  background-image:
    linear-gradient(170deg, rgba(67, 97, 238, 0.08) 0%, rgba(58, 134, 255, 0.02) 80%);
  background-attachment: fixed;
}

/* 保存按钮 */
.edit-profile-save-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

/* 光泽效果 */
.edit-profile-save-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.6s ease;
  z-index: 1;
}

.edit-profile-save-btn:active {
  transform: scale(0.92);
  background: rgba(255, 255, 255, 0.3);
}

.edit-profile-save-btn:active::after {
  left: 100%;
}

.edit-profile-save-btn__icon {
  font-size: 18px;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* 头部区域 */
.edit-profile-header {
  position: relative;
  height: 180px; /* 减少高度以适应 */
  overflow: hidden;
  border-radius: 0 0 30px 30px;
  box-shadow: 0 10px 20px rgba(31, 60, 136, 0.1);
  margin-top: -1px; /* 消除间隙 */
}

.edit-profile-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* 蓝色渐变背景 */
  z-index: 1;
  box-shadow: 0 8px 24px rgba(67, 97, 238, 0.15);
}

/* 头像区域 */
.edit-profile-avatar-wrapper {
  position: relative;
  z-index: 3;
  display: flex;
  justify-content: center;
  padding: 30px 0;
}

.edit-profile-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 18px;
  padding: 22px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
}

/* 容器光泽效果 */
.edit-profile-avatar-container::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 6s infinite;
  z-index: 0;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(30deg);
  }
}

.edit-profile-avatar {
  width: 100px;
  height: 100px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

/* 头像边框光效 */
.edit-profile-avatar::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: conic-gradient(
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.3) 25%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.3) 75%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: 50%;
  z-index: -1;
  animation: rotate 6s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.edit-profile-avatar:active {
  transform: scale(0.95);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.edit-profile-avatar-hint {
  font-size: 14px;
  color: #FFFFFF;
  margin-top: 14px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 2;
  font-weight: 500;
  letter-spacing: 0.2px;
}

/* 表单区域 */
.edit-profile-form-container {
  padding: 0 16px;
  margin-top: 30px;
  padding-bottom: 24px;
}

.edit-profile-card {
  background-color: #FFFFFF;
  border-radius: 20px;
  margin-bottom: 18px;
  overflow: hidden;
  box-shadow: 0 10px 28px rgba(31, 60, 136, 0.07),
              0 4px 12px rgba(0, 0, 0, 0.04);
  position: relative;
  background: #FFFFFF;
  border: 1px solid rgba(58, 134, 255, 0.1); /* 添加淡蓝色边框 */
  transition: all 0.3s ease;
}

/* 移除卡片光效 */
.edit-profile-card::before {
  display: none;
}

.edit-profile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  position: relative;
  z-index: 1;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.1) 0%, rgba(58, 134, 255, 0.05) 100%);
}

.edit-profile-card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2E3A59;
  letter-spacing: 0.2px;
}

.edit-profile-card-body {
  background-color: #FFFFFF;
  position: relative;
  z-index: 1;
  padding: 8px 0;
}

/* 表单项样式 */
.edit-profile-form-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  min-height: 60px;
  transition: all 0.3s ease;
}

.edit-profile-form-item:active {
  background-color: rgba(67, 97, 238, 0.03);
}

.edit-profile-form-item__label {
  font-size: 15px;
  color: #3a86ff; /* 使用主题蓝色 */
  font-weight: 500;
  width: 80px;
  letter-spacing: 0.2px;
}

.edit-profile-form-item__input {
  flex: 1;
  height: 42px;
  border: 1px solid rgba(67, 97, 238, 0.15);
  border-radius: 12px;
  padding: 0 14px;
  font-size: 14px;
  color: #2E3A59;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.05);
}

.edit-profile-form-item__input:focus {
  border-color: #4361ee;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
  background-color: #FFFFFF;
}

.edit-profile-divider {
  height: 1px;
  background-color: rgba(240, 243, 250, 0.8);
  margin: 0 16px;
}

/* 性别选择 */
.edit-profile-gender {
  display: flex;
  flex-direction: row;
}

.edit-profile-gender__option {
  width: 64px;
  height: 38px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  background-color: #F8F9FA;
  border: 1px solid rgba(67, 97, 238, 0.15);
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.05);
  position: relative;
  overflow: hidden;
}

.edit-profile-gender__option:active {
  transform: scale(0.95);
}

.edit-profile-gender__option--active {
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* 蓝色渐变背景 */
  border: none;
  box-shadow: 0 6px 16px rgba(67, 97, 238, 0.15);
}

/* 光泽效果 */
.edit-profile-gender__option::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.6s ease;
  opacity: 0;
}

.edit-profile-gender__option:active::after {
  left: 100%;
  opacity: 1;
}

.edit-profile-gender__text {
  font-size: 14px;
  color: #5E6C84;
  font-weight: 500;
  transition: all 0.3s ease;
}

.edit-profile-gender__option--active .edit-profile-gender__text {
  color: #FFFFFF;
  font-weight: 600;
}

/* 选择器样式 */
.edit-profile-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  height: 42px;
  padding: 0 14px;
  border: 1px solid rgba(67, 97, 238, 0.15);
  border-radius: 12px;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.05);
  position: relative;
  overflow: hidden;
}

.edit-profile-picker:active {
  background-color: rgba(67, 97, 238, 0.03);
  transform: translateY(1px);
}

/* 光泽效果 */
.edit-profile-picker::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.6s ease;
  opacity: 0;
}

.edit-profile-picker:active::after {
  left: 100%;
  opacity: 1;
}

.edit-profile-picker__text {
  font-size: 14px;
  color: #2E3A59;
  margin-right: 8px;
}

.edit-profile-picker__arrow {
  font-size: 12px;
  color: #3a86ff; /* 使用主题蓝色 */
}

/* 个性签名 */
.edit-profile-signature-container {
  width: 100%;
  padding: 16px;
  position: relative;
}

.edit-profile-signature-input {
  width: 100%;
  height: 120px;
  border: 1px solid rgba(67, 97, 238, 0.15);
  border-radius: 12px;
  padding: 14px;
  font-size: 14px;
  color: #2E3A59;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(67, 97, 238, 0.05);
  line-height: 1.6;
}

.edit-profile-signature-input:focus {
  border-color: #4361ee;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
  background-color: #FFFFFF;
}

.edit-profile-signature-count {
  position: absolute;
  bottom: 24px;
  right: 24px;
  font-size: 12px;
  color: #5E6C84;
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 8px;
  backdrop-filter: blur(4px);
}

/* 提交按钮 */
.edit-profile-submit {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 30px;
  margin-bottom: 40px;
}

.edit-profile-submit-btn {
  width: 220px;
  height: 52px;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* 蓝色渐变背景 */
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

/* 光泽效果 */
.edit-profile-submit-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.8s ease;
  opacity: 0;
}

.edit-profile-submit-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4px 12px rgba(67, 97, 238, 0.15);
}

.edit-profile-submit-btn:active::after {
  left: 100%;
  opacity: 1;
}

.edit-profile-submit-btn__text {
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
</style>

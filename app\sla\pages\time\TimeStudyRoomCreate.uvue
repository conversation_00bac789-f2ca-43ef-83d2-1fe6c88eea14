<template>
  <view class="create-room-container">
    <!-- 顶部导航栏 -->
    <sla-navbar
      title="创建自习室"
      :show-back="true"
      @back="goBack"
    ></sla-navbar>

    <!-- 表单内容 -->
    <scroll-view class="form-scroll" scroll-y>
      <view class="form-container">
        <view class="form-section">
          <view class="form-item">
            <text class="form-label">自习室名称</text>
            <input
              class="form-input"
              type="text"
              v-model="createForm.name as string"
              placeholder="请输入2-64个字符"
              maxlength="64"
            />
          </view>

          <view class="form-item">
            <text class="form-label">自习室描述</text>
            <input
              class="form-input"
              type="text"
              v-model="createForm.description as string"
              placeholder="请输入描述，最多255个字符"
              maxlength="255"
            />
          </view>
        </view>

        <view class="form-section">
          <view class="form-item">
            <text class="form-label">容量 ({{ createForm.capacity }}人)</text>
            <slider
              class="form-slider"
              min="2"
              max="20"
              show-value
              :value="createForm.capacity"
              @change="handleCapacityChange"
              activeColor="#80B8F5"
              backgroundColor="#E8E8E8"
            ></slider>
          </view>

          <view class="form-item">
            <view class="public-row">
              <text class="form-label-inline">公开房间</text>
              <switch
                class="form-switch"
                color="#80B8F5"
                :checked="createForm.isPublic"
                @change="handlePublicChange"
              ></switch>
              <text class="form-hint">{{ createForm.isPublic ? '任何人都可以加入' : '需要密码才能加入' }}</text>
            </view>
          </view>

          <view class="form-item" v-if="!createForm.isPublic">
            <text class="form-label">密码</text>
            <input
              class="form-input"
              type="password"
              v-model="createForm.password as string"
              placeholder="请输入密码"
            />
          </view>
        </view>

        <!-- 标签设置 -->
        <view class="form-section">
          <view class="form-item">
            <view class="tags-header">
              <view class="tags-title-row">
                <text class="form-label tag-label">标签</text>
                <text class="tags-count">{{ selectedTags.length }}/5</text>
              </view>
            </view>

            <!-- 已选标签 - 展示 -->
            <view class="tags-container" v-if="selectedTags.length > 0">
              <view class="tag-item" v-for="(tag, index) in selectedTags" :key="index">
                <text class="tag-text">{{ tag }}</text>
                <view class="tag-remove-btn" @click.stop="removeTag(index)">
                  <text class="tag-remove-icon">×</text>
                </view>
              </view>
            </view>

            <!-- 添加标签按钮 - 展示 -->
            <view class="tag-add-container" v-if="!showTagInput">
              <view class="tag-add-btn" @click="showAddTagInput" v-if="selectedTags.length < 5">
                <text class="tag-add-icon">+</text>
                <text class="tag-add-text">添加标签</text>
              </view>
            </view>

            <!-- 标签输入 -->
            <view class="tag-input-container" v-if="showTagInput">
              <input
                class="tag-input"
                type="text"
                v-model="newTag as string"
                placeholder="输入标签(最多10字)"
                maxlength="10"
                focus
                @confirm="addNewTag"
              />
              <view class="tag-action-row">
                <view class="tag-btn tag-cancel-btn" @click="cancelAddTag">取消</view>
                <view class="tag-btn tag-confirm-btn" @click="addNewTag">确定</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 环境选择（2x2网格布局） -->
        <view class="form-section">
          <view class="form-item">
            <text class="form-label">环境</text>
            <view class="options-grid">
              <view
                v-for="(env, index) in environments"
                :key="index"
                class="option-cell"
                :class="{ 'selected': createForm.environment === env.value }"
                @click="selectEnvironment(env.value)"
              >
                <image class="env-image" :src="env.image" mode="aspectFill"></image>
                <view class="option-overlay"></view>
                <text class="option-name">{{ env.name }}</text>
              </view>
            </view>
          </view>

          <!-- 环境预览图 -->
          <view class="env-preview">
            <image
              class="preview-image"
              :src="getSelectedEnvironmentImage()"
              mode="aspectFill"
            ></image>
            <view class="preview-overlay"></view>
          </view>
        </view>

        <!-- 背景音乐选择 - 网格 -->
        <view class="form-item">
          <text class="form-label">背景音乐</text>
          <view class="options-grid music-grid">
            <view
              v-for="(bgm, index) in backgroundMusics"
              :key="index"
              class="option-cell music-cell"
              :class="{ 'selected': createForm.backgroundMusic === bgm.value }"
              @click="selectBackgroundMusic(bgm.value)"
            >
              <view class="music-cell-overlay"></view>
              <view class="music-cell-content">
                <text class="iconfont" :class="bgm.icon"></text>
                <text class="option-name">{{ bgm.name }}</text>
                <view class="preview-icon" @click.stop="previewBackgroundMusic(bgm.value)" v-if="bgm.value !== 'none'">
                  <text class="preview-icon-text">▶</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button
        class="action-button cancel-button"
        @click="goBack"
      >取消</button>
      <button
        class="action-button create-button"
        @click="createRoom"
      >创建自习室</button>
    </view>
  </view>
</template>

<script>
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import SlaButton from '../../components/user/SlaButton.uvue';
// 导入API
import { createStudyRoom } from '../../utils/api/time.js';

interface CreateRoomForm {
  name: string;
  description: string;
  capacity: number;
  isPublic: boolean;
  password: string;
  environment: string;
  backgroundMusic: string;
  tags: string[];
}

interface ApiResponse {
  code: number;
  message: string;
  data: any;
}

interface EnvOption {
  name: string;
  value: string;
  icon: string;
  image: string;
}

interface BgmOption {
  name: string;
  value: string;
  icon: string;
  sound: string;
}

export default {
  components: {
    SlaNavbar,
    SlaButton
  },
  data() {
    return {
      statusBarHeight: 20, // 状态栏高度

      // 表单数据
      createForm: {
        name: '',
        description: '',
        capacity: 8,
        isPublic: true,
        password: '',
        environment: 'library',
        backgroundMusic: 'none',
        tags: []
      } as CreateRoomForm,

      // 环境选项
      environments: [
        { name: '图书馆', value: 'library', icon: 'icon-library', image: '/static/sounds/图书馆.jpg' },
        { name: '咖啡厅', value: 'cafe', icon: 'icon-cafe', image: '/static/sounds/咖啡厅.jpg' },
        { name: '教室', value: 'classroom', icon: 'icon-classroom', image: '/static/sounds/教室.png' },
        { name: '自然', value: 'nature', icon: 'icon-nature', image: '/static/sounds/自然.jpg' }
      ] as EnvOption[],

      // 背景音乐选项
      backgroundMusics: [
        { name: '静音', value: 'none', icon: 'icon-mute', sound: '' },
        { name: '雨声', value: 'rain', icon: 'icon-rain', sound: '/static/sounds/雨声.mp3' },
        { name: '咖啡厅', value: 'cafe', icon: 'icon-music', sound: '/static/sounds/咖啡厅.mp3' },
        { name: '自然', value: 'nature', icon: 'icon-music', sound: '/static/sounds/自然.mp3' }
      ] as BgmOption[],

      // 标签相关
      selectedTags: [] as string[],
      showTagInput: false,
      newTag: '',
      audioContext: null as any
    };
  },
  onLoad() {
    // 获取系统信息
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;
  },

  onUnload() {
    // 清理音频上下文
    if (this.audioContext) {
      try {
        this.audioContext.stop();
        this.audioContext.destroy();
      } catch (e) {
        console.warn('清理音频上下文失败:', e);
      }
    }
  },
  methods: {
    // 返回到上一页
    goBack() {
      // 设置需要刷新自习室列表标志
      uni.setStorageSync('needRefreshStudyRoomList', true);

      // 返回到时间管理页面
      uni.switchTab({
        url: '/pages/time/index'
      });

      // 这里不需要使用navigateBack
      // 因为index.uvue是Tab页面
    },

    // 处理容量变化
    handleCapacityChange(e) {
      this.createForm.capacity = e.detail.value;
    },

    // 处理公开状态变化
    handlePublicChange(e) {
      this.createForm.isPublic = e.detail.value;
      if (this.createForm.isPublic) {
        this.createForm.password = '';
      }
    },

    // 选择环境
    selectEnvironment(value) {
      this.createForm.environment = value;
    },

    // 选择背景音乐
    selectBackgroundMusic(value) {
      this.createForm.backgroundMusic = value;

      // 预览音乐
      this.previewBackgroundMusic(value);
    },

    // 移除标签
    removeTag(index) {
      this.selectedTags.splice(index, 1);
    },

    // 显示添加标签输入框
    showAddTagInput() {
      if (this.selectedTags.length >= 5) {
        uni.showToast({
          title: '最多只能添加5个标签',
          icon: 'none'
        });
        return;
      }
      this.showTagInput = true;
      this.newTag = '';
    },

    // 取消添加标签
    cancelAddTag() {
      this.showTagInput = false;
      this.newTag = '';
    },

    // 添加新标签
    addNewTag() {
      if (!this.newTag.trim()) {
        uni.showToast({
          title: '标签不能为空',
          icon: 'none'
        });
        return;
      }

      if (this.newTag.length > 10) {
        uni.showToast({
          title: '标签长度不能超过10个字符',
          icon: 'none'
        });
        return;
      }

      const newTag = this.newTag.trim();

      // 检查重复
      if (this.selectedTags.includes(newTag)) {
        uni.showToast({
          title: '标签已存在',
          icon: 'none'
        });
        return;
      }

      // 添加标签
      if (this.selectedTags.length < 5) {
        this.selectedTags.push(newTag);
        this.showTagInput = false;
        this.newTag = '';
      } else {
        uni.showToast({
          title: '最多只能添加5个标签',
          icon: 'none'
        });
      }
    },

    // 创建自习室
    createRoom() {
      // 表单验证
      if (!this.createForm.name.trim()) {
        uni.showToast({
          title: '请输入房间名称',
          icon: 'none'
        });
        return;
      }

      if (this.createForm.name.length < 2 || this.createForm.name.length > 64) {
        uni.showToast({
          title: '房间名称长度应为2-64个字符',
          icon: 'none'
        });
        return;
      }

      if (!this.createForm.isPublic && !this.createForm.password) {
        uni.showToast({
          title: '非公开房间需要设置密码',
          icon: 'none'
        });
        return;
      }

      // 设置标签
      this.createForm.tags = [...this.selectedTags];

      uni.showLoading({
        title: '创建中...'
      });

      // 调用API创建自习室
      createStudyRoom(this.createForm)
        .then(response => {
          uni.showToast({
            title: '创建自习室成功',
            icon: 'success'
          });

          // 获取房间ID
          const roomId = response.data.roomId;

          // 保存最近加入的房间
          uni.setStorageSync('lastJoinedRoom', roomId);

          // 延迟跳转
          setTimeout(() => {
            // 设置需要刷新列表
            uni.setStorageSync('needRefreshStudyRoomList', true);

            // 使用redirectTo或navigateTo跳转到房间详情
            uni.redirectTo({
              url: '/pages/time/TimeStudyRoomDetail?roomId=' + roomId + '&fromCreate=true'
            });
          }, 1000);
        })
        .catch(error => {
          console.error('创建自习室失败:', error);
          // 显示timeRequest
        })
        .finally(() => {
          uni.hideLoading();
        });
    },

    // 获取选中环境的图片
    getSelectedEnvironmentImage() {
      const selectedEnv = this.environments.find(env => env.value === this.createForm.environment);
      return selectedEnv ? selectedEnv.image : this.environments[0].image;
    },

    // 预览背景音乐
    previewBackgroundMusic(value: string) {
      const selectedMusic = this.backgroundMusics.find(bgm => bgm.value === value);
      if (selectedMusic && selectedMusic.sound) {
        try {
          // 检查音频API是否可用
          if (typeof uni.createInnerAudioContext === 'function') {
            // 停止之前的音频
            if (this.audioContext) {
              try {
                this.audioContext.stop();
              } catch (e) {
                console.warn('停止之前的音频失败:', e);
              }
            }

            // 创建新的音频上下文
            const newAudioContext = uni.createInnerAudioContext();
            newAudioContext.src = selectedMusic.sound;
            newAudioContext.loop = true;

            // 保存音频上下文
            this.audioContext = newAudioContext;

            // 播放音乐
            if (value !== 'none') {
              this.audioContext.play();
            }
          } else {
            // 音频API不可用的处理
            console.warn('音频API不可用或不支持');
            uni.showToast({
              title: '音频API不可用或不支持',
              icon: 'none'
            });
          }
        } catch (error) {
          console.error('预览背景音乐失败:', error);
          uni.showToast({
            title: '预览背景音乐失败',
            icon: 'none'
          });
        }
      }
    },
  }
};
</script>

<style>
.create-room-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

/* ?????? */
.form-scroll {
  flex: 1;
  margin-top: 44px; /* ????? */
}

.form-container {
  padding: 16px;
}

.form-section {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 16px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
}

.public-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 4px;
}

.form-label-inline {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-right: 12px;
  flex-shrink: 0;
}

.form-switch {
  margin-right: 8px;
  transform: scale(0.8);
}

.form-hint {
  font-size: 12px;
  color: #999;
  flex: 1;
}

.form-input {
  width: 100%;
  height: 36px;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #f9f9f9;
}

.form-textarea {
  width: 100%;
  height: 100px;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  background-color: #f9f9f9;
}

.form-slider {
  margin: 10px 0;
}

/* ?????? */
.tags-header {
  margin-bottom: 6px;
}

.tags-title-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.tag-label {
  margin-bottom: 0;
}

.tags-count {
  font-size: 12px;
  color: #999;
  background-color: #f0f4ff;
  padding: 2px 6px;
  border-radius: 10px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
  margin-top: 6px;
  justify-content: flex-start;
  gap: 4px;
  padding: 1px;
  width: 100%;
}

.tag-item {
  position: relative;
  display: inline-flex;
  align-items: center;
  background-color: #f0f4ff;
  border-radius: 8px;
  padding: 2px 6px;
  margin-right: 4px;
  margin-bottom: 4px;
  border: 1px solid #e0e6ff;
  max-width: 25%;
  transition: all 0.2s ease;
}

.tag-text {
  font-size: 10px;
  color: #4361D8;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  margin-right: 2px;
}

.tag-remove-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e0e6ff;
  margin-left: 1px;
  transition: all 0.2s ease;
}

.tag-remove-btn:active {
  transform: scale(0.9);
  background-color: #d0d6ff;
}

.tag-remove-icon {
  font-size: 8px;
  color: #4361D8;
  font-weight: bold;
  line-height: 1;
}

.tag-add-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.tag-add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #c0d0ff;
  border-radius: 10px;
  padding: 2px 6px;
  width: fit-content;
  background-color: rgba(91, 127, 255, 0.03);
  transition: all 0.2s ease;
}

.tag-add-btn:active {
  transform: scale(0.95);
  background-color: rgba(91, 127, 255, 0.08);
}

.tag-add-icon {
  font-size: 10px;
  color: #5B7FFF;
  margin-right: 2px;
  font-weight: bold;
}

.tag-add-text {
  font-size: 10px;
  color: #5B7FFF;
  font-weight: 500;
}

.tag-input-container {
  margin-top: 6px;
  border: 1px solid #e0e6ff;
  border-radius: 10px;
  padding: 10px;
  background-color: #f9faff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.tag-input {
  width: 100%;
  height: 32px;
  border: 1px solid #d0d9ff;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 12px;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.tag-action-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}

.tag-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  border-radius: 15px;
  width: 48%;
  text-align: center;
  height: 28px;
  transition: all 0.2s ease;
}

.tag-btn:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.tag-cancel-btn {
  color: #666;
  background-color: #f0f0f0;
  border: 1px solid #e0e0e0;
}

.tag-confirm-btn {
  background: linear-gradient(135deg, #6495ED, #4F7FFF);
  color: white;
  box-shadow: 0 2px 4px rgba(91, 127, 255, 0.2);
}

/* ???? - 2�2???? */
.options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 2? */
  grid-template-rows: repeat(2, 80px); /* 2????80px */
  gap: 12px; /* ?? */
  margin-top: 10px;
}

.option-cell {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  transition: all 0.2s ease;
  overflow: hidden;
}

.option-cell:active {
  transform: scale(0.98);
}

.env-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.option-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.option-cell.selected .option-overlay {
  background-color: rgba(0, 0, 0, 0.2);
}

.option-cell .iconfont {
  font-size: 28px;
  color: #666;
  margin-bottom: 8px;
  z-index: 3;
}

.option-name {
  font-size: 14px;
  color: #666;
  z-index: 3;
}

.option-cell.selected {
  background-color: #f0f4ff;
  border: 1px solid #80B8F5;
  box-shadow: 0 0 0 2px rgba(128, 184, 245, 0.3);
}

/* ????? */
.env-preview {
  width: 100%;
  height: 150px;
  margin-top: 15px;
  margin-bottom: 15px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.2);
}

/* ?????? - ?? */
.music-grid {
  gap: 12px;
}

.music-cell {
  border: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

/* ?????? */
.music-cell:nth-child(1) {
  background-color: #6A7FDB; /* ??? - ??? */
}

.music-cell:nth-child(2) {
  background-color: #5C9EAD; /* ?? - ?? */
}

.music-cell:nth-child(3) {
  background-color: #A57548; /* ??? - ??? */
}

.music-cell:nth-child(4) {
  background-color: #5A9367; /* ?? - ?? */
}

.music-cell.selected {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.music-cell-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
  z-index: 2;
}

.music-cell.selected .music-cell-overlay {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.music-cell-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 10px;
}

/* ???? */
.preview-icon {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 4;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.preview-icon-text {
  color: #333;
  font-size: 12px;
  font-weight: bold;
}

/* ???????? */
.music-cell .option-name {
  font-size: 14px;
  color: white;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  margin-top: 6px;
}

.music-cell .iconfont {
  font-size: 28px;
  color: white;
  margin-bottom: 6px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* ?????? */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
  justify-content: flex-start;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 6px 14px;
  background-color: #f5f5f5;
  border-radius: 16px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
}

.tag-item.selected {
  background-color: #80B8F5;
  color: white;
  box-shadow: 0 2px 4px rgba(128, 184, 245, 0.3);
}

.tag-remove {
  margin-left: 6px;
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
}

.add-tag {
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
}

.custom-tag-input {
  margin-top: 10px;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.tag-input {
  width: 100%;
  height: 40px;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 14px;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.tag-actions {
  display: flex;
  justify-content: flex-end;
}

.cancel-tag, .confirm-tag {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.cancel-tag {
  color: #666;
  margin-right: 10px;
}

.confirm-tag {
  background-color: #80B8F5;
  color: white;
}

/* ???? */
.bottom-actions {
  display: flex;
  flex-direction: row;
  padding: 16px;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-button {
  flex: 1;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  transition: all 0.3s ease;
  border: none;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 45%;
  margin: 0 auto;
}

.cancel-button {
  background-color: #f5f7fa;
  color: #666;
  margin-right: 12px;
  border: 1px solid #e0e0e0;
}

.cancel-button:active {
  background-color: #e8eaed;
  transform: scale(0.98);
}

.create-button {
  background: linear-gradient(135deg, #6495ED, #4F7FFF);
  color: white;
  box-shadow: 0 4px 10px rgba(91, 127, 255, 0.3);
}

.create-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.2);
}
</style>
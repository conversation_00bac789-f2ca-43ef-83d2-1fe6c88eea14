<template>
  <view class="profile-container profile-page">
    <!-- 未登录状态 -->
    <view class="profile-not-login" v-if="!isLoggedIn">
      <image class="profile-not-login__image" src="/static/images/not-login.png" mode="aspectFit"></image>
      <text class="profile-not-login__text">请先登录以查看个人信息</text>
      <sla-button
        text="立即登录"
        type="primary"
        size="medium"
        @click="navigateToLogin"
        class="profile-not-login__button"
      />
    </view>

    <!-- 已登录状态 -->
    <view class="profile-content" v-else>
      <!-- 个人信息头部区域 -->
      <view class="profile-header">
        <view class="profile-header-bg"></view>

        <!-- 导航栏区域 -->
        <sla-navbar title="个人中心" :show-back="false"></sla-navbar>

        <view class="profile-header-info">
          <view class="profile-avatar-container">
            <sla-avatar
              :src="userInfo.avatar"
              size="large"
              :editable="true"
              @change="handleAvatarChange"
              class="profile-avatar"
            />
          </view>
          <view class="profile-info">
            <text class="profile-info__name">{{ truncateName(userInfo.username) }}</text>
            <text class="profile-info__identity">{{ getIdentityText(userInfo.identity) }}</text>
          </view>
          <view class="profile-edit-btn" @click="navigateToEditProfile">
            <text class="profile-edit-btn__text">编辑</text>
          </view>
        </view>
      </view>

      <!-- 基本信息卡片 -->
      <view class="profile-info-card profile-username-card">
        <view class="profile-card-header">
          <text class="profile-card-title">📝 基本信息</text>
        </view>
        <view class="profile-card-body">
          <view class="profile-info-item">
            <text class="profile-info-item__label">用户名</text>
            <text class="profile-info-item__value">{{ truncateName(userInfo.username) }}</text>
          </view>

          <view class="profile-info-divider"></view>

          <view class="profile-info-item">
            <text class="profile-info-item__label">身份</text>
            <text class="profile-info-item__value">{{ getIdentityText(userInfo.identity) }}</text>
          </view>
        </view>
      </view>

      <!-- 个性签名卡片 -->
      <view class="profile-motto-card">
        <view class="profile-motto-header">
          <view class="profile-motto-title-container">
            <text class="profile-motto-header__title">✨ 个性签名</text>
          </view>
        </view>
        <view class="profile-motto-content">
          <text class="profile-motto-text">{{ userInfo.signature || '这个人很懒，什么都没有留下' }}</text>
        </view>
      </view>

      <!-- 功能菜单卡片 -->
      <view class="profile-function-card">
        <view class="profile-card-header">
          <text class="profile-card-title">⚙️ 功能设置</text>
        </view>
        <view class="profile-card-body">
          <view class="profile-card profile-card--password" @click="navigateToChangePassword">
            <view class="profile-card__icon">🔒</view>
            <view class="profile-card__content">
              <text class="profile-card__title">修改密码</text>
            </view>
            <text class="profile-card__arrow">></text>
          </view>

          <view class="profile-card profile-card--feedback" @click="showFeedback">
            <view class="profile-card__icon">💬</view>
            <view class="profile-card__content">
              <text class="profile-card__title">意见反馈</text>
            </view>
            <text class="profile-card__arrow">></text>
          </view>

          <view class="profile-card profile-card--about" @click="showAbout">
            <view class="profile-card__icon">ℹ️</view>
            <view class="profile-card__content">
              <text class="profile-card__title">关于我们</text>
            </view>
            <text class="profile-card__arrow">></text>
          </view>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <view class="profile-logout" @click="handleLogout">
        <text class="profile-logout__text">退出登录</text>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <custom-tab-bar
      :current="4"
      :showAiToolbox="false"
      @centerClick="navigateToAI"
      @tabClick="handleTabClick"
    ></custom-tab-bar>
  </view>
</template>

<script>
import SlaButton from '../../components/user/SlaButton.uvue';
import SlaAvatar from '../../components/user/SlaAvatar.uvue';
import SlaCard from '../../components/user/SlaCard.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import CustomTabBar from '../../components/common/CustomTabBar.uvue';
import { getUserInfo, logout, uploadFile } from '../../utils/api/user.js';

export default {
  components: {
    SlaButton,
    SlaAvatar,
    SlaCard,
    SlaNavbar,
    CustomTabBar
  },
  data() {
    return {
      // 登录状态
      isLoggedIn: false,
      // 用户信息
      userInfo: {
        userId: 0,
        username: '',
        phone: '',
        avatar: '',
        gender: 0,
        identity: 0,
        signature: '',
        birthday: '',
        status: 0,
        createTime: ''
      }
    };
  },
  onShow() {
    // 检查登录状态
    this.checkLoginStatus();

    // 如果已登录，获取用户信息
    if (this.isLoggedIn) {
      this.fetchUserInfo();
    }
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = uni.getStorageSync('accessToken');
      this.isLoggedIn = !!token;

      // 如果已登录，先从缓存获取用户信息
      if (this.isLoggedIn) {
        const cachedUserInfo = uni.getStorageSync('userInfo');
        if (cachedUserInfo) {
          this.userInfo = cachedUserInfo;
        }
      }
    },

    // 获取用户信息
    fetchUserInfo() {
      getUserInfo().then(res => {
        if (res.data) {
          this.userInfo = res.data;
          // 更新缓存
          uni.setStorageSync('userInfo', res.data);
        }
      }).catch(err => {
        console.error('获取用户信息失败', err);
      });
    },

    // 处理头像更改
    handleAvatarChange(filePath) {
      uni.showLoading({
        title: '上传中...'
      });

      // 上传头像
      uploadFile(filePath, 0).then(res => {
        if (res.data) {
          // 更新头像
          this.userInfo.avatar = res.data;
          // 更新缓存
          uni.setStorageSync('userInfo', this.userInfo);

          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
        }
      }).catch(err => {
        uni.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
      }).finally(() => {
        uni.hideLoading();
      });
    },

    // 处理退出登录
    handleLogout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            logout().then(() => {
              uni.showToast({
                title: '退出成功',
                icon: 'success'
              });

              // 清除登录状态和用户信息
              this.isLoggedIn = false;
              this.userInfo = {
                userId: 0,
                username: '',
                phone: '',
                avatar: '',
                gender: 0,
                identity: 0,
                signature: '',
                birthday: '',
                status: 0,
                createTime: ''
              };

              // 清除本地存储
              uni.removeStorageSync('accessToken');
              uni.removeStorageSync('refreshToken');
              uni.removeStorageSync('userInfo');

              // 延迟跳转到登录页
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/user/login'
                });
              }, 1500);
            }).catch(err => {
              uni.showToast({
                title: err.message || '退出失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },

    // 格式化手机号
    formatPhone(phone) {
      if (!phone) return '';
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    // 截断用户名
    truncateName(name) {
      if (!name) return '';
      return name.length > 10 ? name.substring(0, 10) + '...' : name;
    },

    // 获取身份文本
    getIdentityText(identity) {
      const identityOptions = ['初中', '高中', '大学', '研究生', '在职', '其他'];
      return identityOptions[identity] || '未知';
    },

    // 跳转到登录页
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/user/login'
      });
    },

    // 跳转到编辑资料页
    navigateToEditProfile() {
      uni.navigateTo({
        url: '/pages/user/edit-profile'
      });
    },

    // 跳转到修改密码页
    navigateToChangePassword() {
      uni.navigateTo({
        url: '/pages/user/change-password'
      });
    },

    // 意见反馈
    showFeedback() {
      uni.showModal({
        title: '意见反馈',
        content: '感谢您的反馈，我们会持续改进',
        showCancel: false
      });
    },

    // 关于我们
    showAbout() {
      uni.showModal({
        title: '关于我们',
        content: '学习助手 v1.0.0\n专注于提升学习效率',
        showCancel: false
      });
    },

    // 跳转到AI页面
    navigateToAI(showToolbox = false) {
      uni.switchTab({
        url: '/pages/ai/index'
      });

      // 设置是否显示AI工具箱
      uni.setStorageSync('showAiToolbox', showToolbox);
    },

    // 处理标签页点击
    handleTabClick(index) {
      // 这里可以处理CustomTabBar的标签页点击事件
      console.log('Tab clicked:', index);
    }
  }
}
</script>

<style>
.profile-container {
  min-height: 800px;
  background-color: #f7f9fc;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 68px; /* 为底部导航栏留出空间 */
  background-image:
    radial-gradient(circle at 90% 10%, rgba(138, 123, 255, 0.1) 0%, rgba(138, 123, 255, 0) 40%),
    radial-gradient(circle at 10% 90%, rgba(91, 127, 255, 0.08) 0%, rgba(91, 127, 255, 0) 50%),
    linear-gradient(to bottom, #f7faff, #f5f8fc);
  background-attachment: fixed;
}

/* 未登录状态样式 */
.profile-not-login {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(250, 250, 255, 0.8));
  margin: 40px 24px;
  border-radius: 24px;
  box-shadow: 0 16px 32px rgba(91, 127, 255, 0.1),
              0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}

.profile-not-login::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 8s infinite;
  z-index: 0;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(30deg);
  }
}

.profile-not-login__image {
  width: 180px;
  height: 180px;
  margin-bottom: 30px;
  filter: drop-shadow(0 10px 15px rgba(91, 127, 255, 0.2));
  z-index: 1;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.profile-not-login__text {
  font-size: 17px;
  color: #4A6FE3;
  margin-bottom: 30px;
  text-align: center;
  font-weight: bold;
  letter-spacing: 0.2px;
  z-index: 1;
}

.profile-not-login__button {
  width: 220px;
  z-index: 1;
  filter: drop-shadow(0 6px 12px rgba(91, 127, 255, 0.15));
}

/* 个人信息头部区域 */
.profile-header {
  position: relative;
  padding-bottom: 24px;
  margin-bottom: 16px;
  z-index: 2;
  overflow: hidden;
}

.profile-header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, #4361ee, #3a86ff, #4361ee); /* 蓝色渐变 */
  background-size: 300% 300%;
  animation: gradientShift 6s ease infinite;
  z-index: 1;
  box-shadow: 0 8px 24px rgba(67, 97, 238, 0.15);
}

/* 背景渐变动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 用户信息容器 */
.profile-header-info {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 20px 16px 0;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 18px;
  padding: 18px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1),
              0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

/* 信息容器光泽效果 */
.profile-header-info::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: infoShine 6s infinite;
  z-index: 0;
}

@keyframes infoShine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(30deg);
  }
}

.profile-avatar-container {
  margin-right: 18px;
  position: relative;
  z-index: 2;
}

.profile-avatar {
  width: 84px;
  height: 84px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 999px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

/* 头像边框光效 */
.profile-avatar::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: conic-gradient(
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.3) 25%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0.3) 75%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: 50%;
  z-index: -1;
  animation: avatarRotate 6s linear infinite;
}

@keyframes avatarRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.profile-avatar:active {
  transform: scale(0.95);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  text-align: left;
  z-index: 2;
}

.profile-info__name {
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  margin-bottom: 6px;
  background: linear-gradient(120deg, #ffffff, #e0e7ff);
  -webkit-background-clip: text;
  background-clip: text;
  display: flex;
  align-items: center;
  animation: nameGlow 3s ease-in-out infinite;
}

/* 用户名发光效果 */
@keyframes nameGlow {
  0%, 100% { opacity: 0.9; }
  50% { opacity: 1; }
}

.profile-info__identity {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  background: linear-gradient(to right, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.25));
  padding: 5px 12px;
  border-radius: 12px;
  margin-top: 6px;
  display: inline-flex;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 编辑按钮 */
.profile-edit-btn {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  padding: 10px 20px;
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12),
              0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 编辑按钮光泽效果 */
.profile-edit-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: all 0.6s ease;
  opacity: 0;
}

.profile-edit-btn:active::after {
  left: 100%;
  opacity: 1;
}

.profile-edit-btn:active {
  transform: scale(0.95) translateY(2px);
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.profile-edit-btn__text {
  font-size: 15px;
  color: #FFFFFF;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

/* 卡片样式 */
.profile-info-card, .profile-function-card, .profile-motto-card {
  margin: 18px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 28px rgba(31, 60, 136, 0.07),
              0 4px 12px rgba(0, 0, 0, 0.04);
  position: relative;
  background: linear-gradient(145deg, #FFFFFF, #FCFCFF);
  border: 1px solid rgba(240, 243, 250, 0.8);
  transition: all 0.3s ease;
}

.profile-info-card:active, .profile-function-card:active, .profile-motto-card:active {
  transform: translateY(2px);
  box-shadow: 0 5px 15px rgba(31, 60, 136, 0.05);
}

/* 卡片头部 */
.profile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  position: relative;
  z-index: 1;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  background: linear-gradient(145deg, rgba(247, 250, 255, 0.5), rgba(255, 255, 255, 0.5));
}

.profile-card-body {
  background-color: #FFFFFF;
  position: relative;
  z-index: 1;
  padding: 8px 0;
}

/* 卡片标题样式 */
.profile-card-title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
  position: relative;
  display: flex;
  align-items: center;
}

/* 信息项样式 */
.profile-info-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px 18px;
  transition: all 0.3s ease;
}

.profile-info-item:active {
  background-color: rgba(67, 97, 238, 0.03);
}

.profile-info-divider {
  height: 1px;
  background-color: rgba(240, 243, 250, 0.8);
  margin: 0 18px;
}

.profile-info-item__label {
  font-size: 15px;
  color: #5E6C84;
  font-weight: bold;
}

.profile-info-item__value {
  font-size: 15px;
  color: #2E3A59;
  font-weight: bold;
  background: linear-gradient(to right, rgba(91, 127, 255, 0.05), rgba(91, 127, 255, 0.1));
  padding: 6px 14px;
  border-radius: 12px;
  border: 1px solid rgba(91, 127, 255, 0.1);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.05);
}

/* 个性签名卡片 */
.profile-motto-card {
  padding: 0;
  overflow: hidden;
}

.profile-motto-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  position: relative;
  z-index: 1;
  padding: 20px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  background: linear-gradient(145deg, rgba(247, 250, 255, 0.5), rgba(255, 255, 255, 0.5));
}

.profile-motto-title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 个性签名标题样式 */
.profile-motto-header__title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
  position: relative;
  display: flex;
  align-items: center;
}

.profile-motto-content {
  position: relative;
  z-index: 1;
  padding: 18px;
  background: linear-gradient(to bottom right,
              rgba(247, 250, 255, 0.5),
              rgba(255, 255, 255, 0.5));
}

.profile-motto-text {
  font-size: 15px;
  color: #5E6C84;
  line-height: 1.6;
  text-align: center;
  width: 100%;
  display: flex;
  background-color: rgba(91, 127, 255, 0.05);
  padding: 18px;
  border-radius: 14px;
  border: 1px solid rgba(91, 127, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  position: relative;
  font-style: italic;
}





/* 功能卡片样式 */
.profile-card {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-card:last-child {
  border-bottom: none;
}

.profile-card:active {
  background-color: rgba(67, 97, 238, 0.03);
  transform: translateY(1px);
}

.profile-card__icon {
  width: 52px;
  height: 52px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  margin-right: 18px;
  box-shadow: 0 8px 20px rgba(31, 60, 136, 0.08),
              0 3px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.profile-card:active .profile-card__icon {
  transform: scale(0.95);
  box-shadow: 0 4px 10px rgba(31, 60, 136, 0.06);
}

.profile-card--password .profile-card__icon {
  background: linear-gradient(135deg, #4361ee, #3a86ff);
  color: #FFFFFF;
}

.profile-card--feedback .profile-card__icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: #FFFFFF;
}

.profile-card--about .profile-card__icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #FFFFFF;
}

.profile-card__content {
  flex: 1;
}

.profile-card__title {
  font-size: 16px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.2px;
}

/* 箭头样式 */
.profile-card__arrow {
  width: 36px;
  height: 36px;
  border-radius: 999px;
  background: linear-gradient(135deg, #f0f4ff, #ffffff);
  box-shadow: 0 3px 10px rgba(91, 127, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4361ee;
  font-size: 18px;
  margin-left: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(91, 127, 255, 0.15);
  position: relative;
}

.profile-card:active .profile-card__arrow {
  transform: translateX(4px);
  background: linear-gradient(135deg, #eef2ff, #f7f9ff);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.15);
}

/* 退出登录按钮 */
.profile-logout {
  width: calc(100% - 36px);
  margin: 30px 18px 32px;
  border-radius: 16px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #ffffff, #fff5f5);
  box-shadow: 0 10px 25px rgba(255, 77, 79, 0.1),
              0 5px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 77, 79, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}





.profile-logout::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 77, 79, 0) 0%,
    rgba(255, 77, 79, 0.1) 50%,
    rgba(255, 77, 79, 0) 100%
  );
  transition: all 0.6s ease;
  opacity: 0;
}
.profile-logout:active {
  transform: scale(0.98) translateY(2px);
  box-shadow: 0 5px 12px rgba(255, 77, 79, 0.08);
}

.profile-logout:active::before {
  left: 100%;
  opacity: 1;
}

.profile-logout__text {
  color: #FF4D4F;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 0.5px;
}
</style>


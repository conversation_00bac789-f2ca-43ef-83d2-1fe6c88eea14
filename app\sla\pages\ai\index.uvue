<template>
  <view class="ai-chat-widget">
    <!-- 顶部标题 -->
    <view class="ai-chat-header">
      <text class="ai-chat-title">{{ showAiToolbox ? 'AI工具箱' : 'AI聊天助手' }}</text>
      <text v-if="!showAiToolbox" class="ai-chat-subtitle">有任何学习问题都可以问我哦</text>
    </view>

    <!-- AI聊天区域 -->
    <view v-if="!showAiToolbox" class="ai-chat-container">
      <!-- 消息列表 -->
      <scroll-view 
        class="ai-chat-messages" 
        scroll-y 
        :scroll-top="scrollTop"
        @scrolltoupper="loadMoreMessages"
      >
        <view 
          v-for="(message, index) in messages" 
          :key="index" 
          :class="['ai-message', message.type === 'user' ? 'user-message' : '']"
        >
          <view class="ai-avatar">
            <template v-if="message.type === 'user'">
              <image :src="userAvatar" mode="aspectFill"></image>
            </template>
            <template v-else>
              <image :src="botAvatar" mode="aspectFill"></image>
            </template>
          </view>
          <view class="ai-message-content">
            <text class="ai-message-text">{{ message.content }}</text>
            <text class="ai-message-time">{{ message.time }}</text>
            <view v-if="message.isStreaming" class="ai-message-loading">
              <text class="loading-dot">.</text>
              <text class="loading-dot">.</text>
              <text class="loading-dot">.</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 快捷问题 -->
      <view class="ai-quick-questions">
        <view class="ai-quick-questions-grid">
          <view 
            v-for="(question, index) in displayQuestions" 
            :key="index"
            class="ai-quick-question-item"
            @click="useQuickQuestion(question)"
          >
            <text class="ai-quick-question-text">{{ question }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- AI工具箱区域 -->
    <view v-if="showAiToolbox" class="ai-toolbox-container">
      <ai-toolbox></ai-toolbox>
    </view>

    <!-- 底部区域 - 仅在聊天模式显示 -->
    <view v-if="!showAiToolbox" class="ai-bottom-container">
      <!-- 功能工具栏 -->
      <view class="ai-function-toolbar">
        <view class="ai-function-item" @click="useTool('summary')">
          <text class="ai-function-text">文档总结</text>
        </view>
        <view class="ai-function-item" @click="useTool('translate')">
          <text class="ai-function-text">翻译</text>
        </view>
        <view class="ai-function-item" @click="useTool('mistakes')">
          <text class="ai-function-text">错题本</text>
        </view>
      </view>
      
      <!-- 输入区域 -->
      <view class="ai-input-container">
        <view class="ai-input-wrapper">
          <textarea
            class="ai-input-field"
            v-model="inputMessage"
            placeholder="输入问题..."
            confirm-type="send"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            @confirm="handleSendMessage"
          ></textarea>
          <view v-if="!isStreaming" class="ai-send-button" @click="handleSendMessage">
            <view class="ai-send-icon"></view>
          </view>
          <view v-else class="ai-stop-button" @click="stopGeneration">
            <view class="ai-stop-icon">■</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义底部标签栏 -->
    <custom-tab-bar
      :current="2"
      :showAiToolbox="showAiToolbox"
      @tabClick="handleTabClick"
      @centerClick="handleCenterClick"
    ></custom-tab-bar>
  </view>
</template>

<script>
import CustomTabBar from '../../components/common/CustomTabBar.uvue';
import AiToolbox from '../../components/ai/AiToolbox.uvue';
import { sendTextStreamChatMessage } from '../../utils/api/chat.js';

// TypeScript声明
declare class AbortController {
  readonly signal: AbortSignal;
  abort(): void;
}

declare class AbortSignal {
  readonly aborted: boolean;
}

// 消息接口
interface Message {
  type: string;
  content: string;
  time: string;
  isStreaming: boolean; // 是否正在流式生成
}

export default {
  components: {
    CustomTabBar,
    AiToolbox
  },
  data() {
    return {
      inputMessage: '',
      messages: [
        {
          type: 'ai',
          content: '你好！我是AI助手，有什么问题都可以问我哦！',
          time: this.formatTime(new Date()),
          isStreaming: false
        }
      ] as Message[],
      isInputFocused: false,
      isAiTyping: false,
      isStreaming: false,
      scrollTop: 0,
      showAiToolbox: false,
      quickQuestions: [
        '如何提高学习效率？',
        '怎样制定学习计划？',
        '有哪些记忆方法？',
        '如何克服拖延症？',
        '怎样提高专注力？'
      ],
      hasMoreMessages: false,
      messageController: null as AbortController | null, // 控制消息生成
      userAvatar: '/static/images/user-avatar.png',
      botAvatar: '/static/images/ai-active.png',
      isIOS: false
    }
  },
  computed: {
    // 显示问题
    displayQuestions() {
      // 只显示前三个快捷问题
      return this.quickQuestions.slice(0, 3);
    }
  },
  watch: {
    // 监听工具箱状态
    showAiToolbox(newVal) {
      // 如果切换到工具箱，且正在生成消息，则停止生成
      if (newVal && this.isStreaming) {
        this.stopGeneration();
      }
    }
  },
  onLoad() {
    // 检查是否需要显示工具箱
    const showToolbox = uni.getStorageSync('showAiToolbox');
    if (showToolbox) {
      this.showAiToolbox = true;
      uni.removeStorageSync('showAiToolbox'); // 清除标记
    }
  },
  onShow() {
    // 页面显示
  },
  onUnload() {
    // 页面卸载
  },
  methods: {
    // 发送消息
    handleSendMessage(content?: string) {
      // 如果传入了内容或输入框有内容，则发送
      const messageContent = typeof content === 'string' ? content : this.inputMessage;
      
      if (!messageContent || messageContent.trim() === '') return;
      
      // 添加消息
      this.addMessage('user', messageContent);
      
      // 清空输入
      this.inputMessage = '';
      
      // 发送到服务器
      this.sendToServer(messageContent);
    },
    
    // 添加消息
    addMessage(type: string, content: string) {
      const message: Message = {
        type,
        content,
        time: this.formatTime(new Date()),
        isStreaming: type === 'ai' ? true : false
      };
      
      // 添加到消息列表
      this.messages.push(message);
      
      // 更新滚动
      this.updateScroll();
    },
    
    // 发送到服务器
    sendToServer(content: string) {
      this.isAiTyping = true;
      this.isStreaming = true;
      
      // 先添加一条空的AI消息，用于流式显示
      const aiMessageIndex = this.messages.length;
      this.addMessage('ai', '');
      
      // 创建控制器用于取消
      this.messageController = new AbortController();
      
      // 发送消息
      sendTextStreamChatMessage(content, {
        onMessage: (chunk) => {
          // 更新消息内容
          if (aiMessageIndex < this.messages.length) {
            this.messages[aiMessageIndex].content += chunk;
          }
        },
        onComplete: () => {
          // 完成生成
          if (aiMessageIndex < this.messages.length) {
            this.messages[aiMessageIndex].isStreaming = false;
          }
          this.isAiTyping = false;
          this.isStreaming = false;
          this.messageController = null;
        },
        onError: (error) => {
          console.error('消息错误:', error);
        }
      }, this.messageController);
    },
    
    // 格式化时间
    formatTime(date: Date): string {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    
    // 停止生成
    stopGeneration() {
      if (this.messageController) {
        this.messageController.abort();
        this.messageController = null;
      }
      
      // 更新最后一条消息状态
      const lastMessage = this.messages[this.messages.length - 1];
      if (lastMessage && lastMessage.type === 'ai' && lastMessage.isStreaming) {
        lastMessage.isStreaming = false;
        lastMessage.content += '\n[生成已中断]';
      }
      
      this.isAiTyping = false;
      this.isStreaming = false;
    },
    
    // 更新滚动
    updateScroll() {
      // 使用nextTick等待DOM更新
      this.$nextTick(() => {
        // 设置一个足够大的scrollHeight来滚动到底部
        const scrollHeight = 99999; // 一个足够大的数值
        this.scrollTop = scrollHeight;
      });
    },
    
    // 加载更多消息
    loadMoreMessages() {
      // 如果有更多历史消息可加载
      if (this.hasMoreMessages) {
        // 加载逻辑
      }
    },
    
    // 处理输入框获取焦点
    handleInputFocus() {
      this.isInputFocused = true;
    },
    
    // 处理输入框失去焦点
    handleInputBlur() {
      this.isInputFocused = false;
    },
    
    // 使用快捷问题
    useQuickQuestion(question: string) {
      this.handleSendMessage(question);
    },
    
    // 使用工具
    useTool(tool: string) {
      switch(tool) {
        case 'summary':
          uni.navigateTo({
            url: '/pages/ai/tools/summary/index'
          });
          break;
        case 'translate':
          uni.navigateTo({
            url: '/pages/ai/tools/translate/index'
          });
          break;
        case 'mistakes':
          uni.navigateTo({
            url: '/pages/ai/tools/mistakes/index'
          });
          break;
        default:
          break;
      }
    },
    
    // 处理Tab点击
    handleTabClick(index: number) {
      // 处理逻辑
    },
    
    // 处理中心按钮点击
    handleCenterClick() {
      // 切换显示工具箱
      this.showAiToolbox = !this.showAiToolbox;
    }
  }
}
</script>

<style>
.ai-chat-widget {
  --ai-primary: #7c3aed;
  --ai-primary-light: #8b5cf6;
  --ai-primary-dark: #6d28d9;
  --ai-secondary: #ec4899;
  --ai-bg-color: #f8fafc;
  --ai-text-color: #1e293b;
  --ai-border-radius: 12px;
  --ai-shadow: 0 10px 30px rgba(124, 58, 237, 0.1);
  --ai-transition: transform 0.3s ease, opacity 0.3s ease;
  
  min-height: 800px;
  background-color: var(--ai-bg-color);
  color: var(--ai-text-color);
  display: flex;
  flex-direction: column;
  padding-bottom: 68px; /* 为底部标签栏留出空间 */
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.ai-chat-header {
  text-align: center;
  padding: 20px 16px;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: var(--ai-shadow);
}

.ai-chat-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--ai-primary), var(--ai-secondary));
  -webkit-background-clip: text;
  color: transparent;
}

.ai-chat-subtitle {
  color: #64748b;
  font-size: 14px;
}

.ai-chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 700px; /* 可能需要根据实际屏幕调整 */
  background: white;
  margin: 16px 16px 0 16px;
  border-radius: var(--ai-border-radius);
  box-shadow: var(--ai-shadow);
  overflow: hidden;
}

/* 消息列表 */
.ai-chat-messages {
  flex: 1;
  padding: 16px;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏WebKit浏览器的滚动条 */
.ai-chat-messages::-webkit-scrollbar {
  display: none;
}

.ai-message {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 20px;
}

.user-message {
  flex-direction: row-reverse;
}

.ai-avatar {
  width: 36px;
  height: 36px;
  border-radius: 999px;
  overflow: hidden;
  margin: 0 10px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-avatar image {
  width: 100%;
  height: 100%;
}

/* 机器人头像样式 */
.bot-face-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #5B42F3 0%, #00DDEB 100%);
  border-radius: 999px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bot-face {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.bot-eyes {
  display: flex;
  flex-direction: row;
  gap: 6px;
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.bot-eye {
  width: 6px;
  height: 12px;
  background-color: #fff;
  border-radius: 3px;
}

.ai-message-content {
  max-width: 70%;
  padding: 12px 15px;
  border-radius: var(--ai-border-radius);
  position: relative;
  background-color: #f1f5f9;
  line-height: 1.5;
  align-self: flex-start;
}

.user-message .ai-message-content {
  background: linear-gradient(135deg, var(--ai-primary), var(--ai-secondary));
  border-top-right-radius: 4px;
}

.ai-message .ai-message-content {
  border-top-left-radius: 4px;
}

.ai-message-text {
  font-size: 16px;
  word-break: break-word;
}

.user-message .ai-message-text {
  color: #FFFFFF;
}

.ai-message .ai-message-text {
  color: var(--ai-text-color);
}

.ai-message-time {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  position: absolute;
  bottom: -18px;
  right: 8px;
}

.user-message .ai-message-time {
  color: rgba(255, 255, 255, 0.6);
}

.ai-message-loading {
  display: flex;
  flex-direction: row;
  margin-top: 8px;
}

.loading-dot {
  font-size: 20px;
  animation: loading-dot-bounce 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    opacity: 0.4;
    transform: translateY(4px);
  }
  50% {
    opacity: 1;
    transform: translateY(0) translateX(4px);
  }
}

/* 快捷问题 */
.ai-quick-questions {
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.ai-quick-questions-grid {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
}

.ai-quick-question-item {
  display: flex;
  margin-bottom: 8px;
  padding: 8px 16px;
  background-color: #f1f5f9;
  border-radius: 20px;
  transition: var(--ai-transition);
}

.ai-quick-question-item:active {
  background-color: var(--ai-primary-light);
}

.ai-quick-question-text {
  font-size: 14px;
  color: var(--ai-text-color);
}

.ai-quick-question-item:active .ai-quick-question-text {
  color: white;
}

/* 底部容器 */
.ai-bottom-container {
  position: fixed;
  bottom: 68px; /* 为标签栏留出空间 */
  left: 0;
  right: 0;
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

/* 功能工具栏 */
.ai-function-toolbar {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  padding: 10px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.ai-function-item {
  padding: 6px 16px;
  border-radius: 20px;
  background-color: rgba(124, 58, 237, 0.1);
  transition: var(--ai-transition);
}

.ai-function-item:active {
  background-color: rgba(124, 58, 237, 0.2);
}

.ai-function-text {
  font-size: 14px;
  color: var(--ai-primary);
  font-weight: bold;
}

/* 输入区域 */
.ai-input-container {
  position: relative;
  padding: 10px 16px;
}

.ai-input-wrapper {
  position: relative;
}

.ai-input-field {
  width: 100%;
  padding: 12px 50px 12px 16px;
  border: 2px solid transparent;
  border-radius: var(--ai-border-radius);
  background-color: #f1f5f9;
  font-size: 16px;
  resize: none;
  outline: none;
  transition: var(--ai-transition);
  min-height: 50px; /* 最小高度 */
  line-height: 24px;
}

.ai-input-field:focus {
  border-color: var(--ai-primary-light);
  box-shadow: 0 0 0 4px rgba(124, 58, 237, 0.1);
}

.ai-send-button, .ai-stop-button {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 36px;
  height: 36px;
  border-radius: 999px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--ai-transition);
}

.ai-send-button {
  background: linear-gradient(135deg, var(--ai-primary), var(--ai-secondary));
}

.ai-stop-button {
  background: linear-gradient(135deg, var(--ai-primary-dark), #e11d48);
}

.ai-send-button:active, .ai-stop-button:active {
  transform: scale(0.95);
}

/* 发送按钮的图标 */
.ai-send-icon {
  position: relative;
  width: 14px;
  height: 14px;
}

.ai-send-icon:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 14px;
  height: 14px;
  border-top: 2px solid white;
  border-right: 2px solid white;
  transform: rotate(45deg);
}

.ai-send-icon:after {
  content: "";
  position: absolute;
  top: 50%;
  left: -5px;
  width: 20px;
  height: 2px;
  background-color: white;
  transform: translateY(-50%) rotate(45deg);
}

/* 停止图标 */
.ai-stop-icon {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: bold;
}

/* AI工具箱容器 */
.ai-toolbox-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 700px; /* 可能需要根据实际屏幕调整 */
  background: white;
  margin: 16px;
  border-radius: var(--ai-border-radius);
  box-shadow: var(--ai-shadow);
  overflow: hidden;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏WebKit浏览器的滚动条 */
.ai-toolbox-container::-webkit-scrollbar {
  display: none;
}

/* 输入框闪光效果 */
@keyframes input-shine {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 0;
  }
}

.ai-input-field:placeholder-shown {
  background-image: linear-gradient(
    90deg,
    rgba(124, 58, 237, 0) 0%,
    rgba(124, 58, 237, 0.05) 50%,
    rgba(124, 58, 237, 0) 100%
  );
  background-size: 200% 100%;
  animation: input-shine 3s infinite;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .ai-chat-container {
    margin: 8px 8px 0 8px;
  }
  
  .ai-chat-title {
    font-size: 22px;
  }
  
  .ai-input-field {
    padding: 10px 45px 10px 14px;
  }
}
</style>


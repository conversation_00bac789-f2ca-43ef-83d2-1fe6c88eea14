<template>
  <view class="mistakes-container">
    <!-- 页面头部 -->
    <view class="mistakes-header">
      <view class="mistakes-back" @click="goBack">
        <text class="mistakes-back__icon">←</text>
      </view>
      <text class="mistakes-title">错题详情</text>
    </view>

    <!-- 页面内容 -->
    <scroll-view class="mistakes-content" scroll-y>
      <!-- 详情卡片 -->
      <view class="detail-card">
        <view class="detail-header">
          <view class="detail-category">
            <text class="detail-category__text">{{ mistake.categoryName }}</text>
          </view>
          <view class="detail-difficulty">
            <text class="detail-difficulty__label">难度：</text>
            <view class="detail-difficulty__stars">
              <text
                v-for="i in 5"
                :key="i"
                class="detail-difficulty__star"
                :class="{'detail-difficulty__star--active': i <= mistake.difficulty}"
              >★</text>
            </view>
          </view>
        </view>

        <!-- 错题图片 -->
        <image
          v-if="mistake.image"
          class="detail-image"
          :src="mistake.image"
          mode="aspectFit"
        ></image>

        <!-- 错题内容 -->
        <view class="detail-content">
          <text class="detail-content__title">错题内容</text>
          <text class="detail-content__text">{{ mistake.content }}</text>
        </view>

        <!-- 错题类型 -->
        <view class="detail-type">
          <text class="detail-type__label">类型：</text>
          <text class="detail-type__text">{{ mistake.type }}</text>
        </view>

        <!-- 错因分析 -->
        <view class="detail-analysis">
          <text class="detail-analysis__title">分析</text>
          <text class="detail-analysis__text">{{ mistake.analysis }}</text>
        </view>

        <!-- 知识点 -->
        <view class="detail-knowledge">
          <text class="detail-knowledge__title">知识点</text>
          <view class="detail-knowledge__tags">
            <view
              class="detail-knowledge__tag"
              v-for="(point, index) in getKnowledgePoints(mistake.knowledgePoints)"
              :key="index"
            >
              <text class="detail-knowledge__tag-text">{{ point }}</text>
            </view>
          </view>
        </view>

        <!-- 创建时间 -->
        <view class="detail-time">
          <text class="detail-time__text">创建于 {{ formatTime(mistake.createTime) }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="detail-actions">
        <view class="detail-action detail-action--edit" @click="editMistake">
          <text class="detail-action__icon">✏️</text>
          <text class="detail-action__text">编辑</text>
        </view>
        <view class="detail-action detail-action--archive" @click="showArchiveConfirm">
          <text class="detail-action__icon">📦</text>
          <text class="detail-action__text">归档</text>
        </view>
        <view class="detail-action detail-action--delete" @click="showDeleteConfirm">
          <text class="detail-action__icon">🗑️</text>
          <text class="detail-action__text">删除</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getMistakeDetail, archiveMistake, deleteMistake } from '../../../../utils/api/aitool.js';

// 错题接口
interface Mistake {
  id: number;
  categoryId: number;
  categoryName: string;
  image: string;
  content: string;
  type: string;
  analysis: string;
  difficulty: number;
  knowledgePoints: string[] | string;
  isArchived: boolean;
  createTime: string;
  archiveTime?: string;
}

export default {
  data() {
    return {
      mistakeId: 0,
      mistake: {} as Mistake,
      loading: false
    }
  },
  onLoad(options) {
    if (options && options.id) {
      this.mistakeId = parseInt(options.id);
      this.loadMistakeDetail();
    } else {
      uni.showToast({
        title: '参数错误，缺少ID',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 加载错题详情
    loadMistakeDetail() {
      this.loading = true;

      // 显示加载中提示
      uni.showLoading({
        title: '加载中...',
      });

      // 调用API获取错题详情
      getMistakeDetail(this.mistakeId)
        .then(res => {
          console.log('获取错题详情成功:', res);
          if (res.data) {
            this.mistake = res.data;
          } else {
            uni.showToast({
              title: 'API返回数据为空',
              icon: 'none'
            });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        })
        .catch(err => {
          console.error('获取错题详情失败:', err);
          uni.showToast({
            title: 'API请求失败',
            icon: 'none'
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        })
        .finally(() => {
          this.loading = false;
          uni.hideLoading();
        });
    },

    // 编辑错题
    editMistake() {
      uni.navigateTo({
        url: `/pages/ai/tools/mistakes/add?id=${this.mistake.id}`
      });
    },

    // 显示归档确认弹窗
    showArchiveConfirm() {
      uni.showModal({
        title: '归档确认',
        content: '确定要归档这道错题吗？归档后可在归档列表中查看。',
        success: (res) => {
          if (res.confirm) {
            // 显示加载中提示
            uni.showLoading({
              title: '归档中...',
            });

            // 调用API归档错题
            archiveMistake(this.mistake.id)
              .then(res => {
                console.log('归档错题成功:', res);
                
                if (res.code === 200) {
                  uni.showToast({
                    title: '归档成功',
                    icon: 'success'
                  });

                  // 返回上一页
                  setTimeout(() => {
                    uni.navigateBack();
                  }, 1500);
                } else {
                  throw new Error(res.message || '归档失败');
                }
              })
              .catch(err => {
                console.error('归档错题失败:', err);
                uni.showToast({
                  title: '归档失败: ' + (err.message || '请稍后再试'),
                  icon: 'none'
                });
              })
              .finally(() => {
                uni.hideLoading();
              });
          }
        }
      });
    },

    // 显示删除确认弹窗
    showDeleteConfirm() {
      uni.showModal({
        title: '删除确认',
        content: '确定要删除这道错题吗？删除后无法恢复。',
        confirmColor: '#FF3B30',
        success: (res) => {
          if (res.confirm) {
            // 显示加载中提示
            uni.showLoading({
              title: '删除中...',
            });

            // 调用API删除错题
            deleteMistake(this.mistake.id)
              .then(res => {
                console.log('删除错题成功:', res);
                
                if (res.code === 200) {
                  uni.showToast({
                    title: '删除成功',
                    icon: 'success'
                  });

                  // 返回上一页
                  setTimeout(() => {
                    uni.navigateBack();
                  }, 1500);
                } else {
                  throw new Error(res.message || '删除失败');
                }
              })
              .catch(err => {
                console.error('删除错题失败:', err);
                uni.showToast({
                  title: '删除失败: ' + (err.message || '请稍后再试'),
                  icon: 'none'
                });
              })
              .finally(() => {
                uni.hideLoading();
              });
          }
        }
      });
    },

    // 格式化时间
    formatTime(timeString: string): string {
      const date = new Date(timeString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    },

    // 获取知识点
    getKnowledgePoints(points) {
      if (Array.isArray(points)) {
        return points;
      }
      // 尝试解析JSON字符串
      if (typeof points === 'string') {
        try {
          const parsed = JSON.parse(points);
          return Array.isArray(parsed) ? parsed : [points];
        } catch (e) {
          // 解析失败，返回原始字符串
          return points ? [points] : [];
        }
      }
      return [];
    }
  }
}
</script>

<style>
@import './styles.css';

/* 详情页特有样式 */
.detail-card {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.detail-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.detail-category {
  padding: 6px 12px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 59, 48, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
}

.detail-category__text {
  font-size: 14px;
  font-weight: bold;
  color: #FF3B30;
}

.detail-difficulty {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.detail-difficulty__label {
  font-size: 14px;
  color: #666666;
  margin-right: 4px;
}

.detail-difficulty__stars {
  display: flex;
  flex-direction: row;
}

.detail-difficulty__star {
  font-size: 16px;
  color: #E0E0E0;
  margin-right: 2px;
}

.detail-difficulty__star--active {
  color: #FF3B30;
}

.detail-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.detail-content {
  margin-bottom: 16px;
}

.detail-content__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}

.detail-content__text {
  font-size: 16px;
  color: #333333;
  line-height: 1.6;
}

.detail-type {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}

.detail-type__label {
  font-size: 14px;
  color: #666666;
  margin-right: 4px;
}

.detail-type__text {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
}

.detail-analysis {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 12px;
}

.detail-analysis__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}

.detail-analysis__text {
  font-size: 16px;
  color: #333333;
  line-height: 1.6;
}

.detail-knowledge {
  margin-bottom: 16px;
}

.detail-knowledge__title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  display: flex;
}

.detail-knowledge__tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.detail-knowledge__tag {
  padding: 6px 12px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 59, 48, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
  margin-right: 8px;
  margin-bottom: 8px;
}

.detail-knowledge__tag-text {
  font-size: 14px;
  color: #FF3B30;
}

.detail-time {
  border-top: 1px solid #F0F0F0;
  padding-top: 16px;
}

.detail-time__text {
  font-size: 14px;
  color: #999999;
}

.detail-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 30px;
}

.detail-action {
  flex: 1;
  height: 80px;
  background-color: #FFFFFF;
  border-radius: 16px;
  margin: 0 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.detail-action__icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.detail-action__text {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
}

.detail-action--edit {
  border-top: 3px solid #4A7BDB;
}

.detail-action--archive {
  border-top: 3px solid #FF9500;
}

.detail-action--delete {
  border-top: 3px solid #FF3B30;
}

.popup-message {
  font-size: 16px;
  color: #666666;
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.5;
}

.popup-action--danger {
  background: linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%);
}
</style>


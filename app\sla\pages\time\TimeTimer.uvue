<template>
  <!-- 计时器Tab内容 -->
  <view>
    <!-- 专注统计 -->
    <view class="focus-section">
      <view class="time-settings__title-row">
        <text class="time-settings__title">今日专注</text>
      </view>

      <view class="focus-stats">
        <view class="focus-stat">
          <text class="focus-stat__value">{{ focusCount }}</text>
          <text class="focus-stat__label">专注次数</text>
        </view>
        <view class="focus-stat">
          <text class="focus-stat__value">{{ totalFocusTime }}</text>
          <text class="focus-stat__label">专注时长(分钟)</text>
        </view>
      </view>
    </view>

    <!-- 计时器模式选择器 -->
    <view class="timer-mode-selector">
      <view class="timer-mode-title-row">
        <text class="timer-mode-title">计时模式</text>
      </view>
      <view class="timer-mode-options">
        <view
          class="timer-mode-option"
          :class="{'timer-mode-option--active': timerMode === 'pomodoro'}"
          @click="switchTimerMode('pomodoro')"
        >
          <text class="timer-mode-option__text">番茄钟</text>
        </view>
        <view
          class="timer-mode-option"
          :class="{'timer-mode-option--active': timerMode === 'countdown'}"
          @click="switchTimerMode('countdown')"
        >
          <text class="timer-mode-option__text">倒计时</text>
        </view>
        <view
          class="timer-mode-option"
          :class="{'timer-mode-option--active': timerMode === 'stopwatch'}"
          @click="switchTimerMode('stopwatch')"
        >
          <text class="timer-mode-option__text">正计时</text>
        </view>
      </view>
    </view>

    <!-- 计时器 -->
    <view class="timer-card">
      <!-- 任务输入框 -->
      <view class="task-input-container" v-if="!isTimerRunning">
        <input class="task-input" type="text" v-model="taskName" placeholder="输入任务名称" />
      </view>

      <!-- 任务名称显示 -->
      <view class="task-name-display" v-if="isTimerRunning && taskName">
        <text class="task-name-text">{{ taskName }}</text>
      </view>

      <view class="timer-circle" :class="timerCircleClass">
        <text class="timer-time">{{ formattedTime }}</text>
        <text class="timer-label">{{ timerLabel }}</text>
      </view>

      <view class="timer-controls">
        <!-- 倒计时模式下显示设置时间按钮 -->
        <view class="timer-button timer-button--secondary" @click="setCustomCountdownTime" v-if="timerMode === 'countdown' && !isTimerRunning && elapsedTime === 0">
          <text class="timer-button__text">设置时间</text>
        </view>

        <!-- 初始状态（未开始） -->
        <template v-if="elapsedTime === 0 && !isTimerRunning">
          <!-- 开始按钮 -->
          <view class="timer-button timer-button--primary" @click="toggleTimer">
            <text class="timer-button__text">开始</text>
          </view>
        </template>

        <!-- 已开始或暂停状态 -->
        <template v-else>
          <!-- 暂停状态下的按钮 -->
          <template v-if="!isTimerRunning">
            <!-- 继续按钮 -->
            <view class="timer-button timer-button--primary" @click="toggleTimer">
              <text class="timer-button__text">继续</text>
            </view>

            <!-- 结束按钮 -->
            <view class="timer-button timer-button--danger" @click="endTimer">
              <text class="timer-button__text">结束</text>
            </view>
          </template>

          <!-- 运行状态下的按钮 -->
          <template v-else>
            <!-- 暂停按钮 -->
            <view class="timer-button timer-button--warning" @click="toggleTimer">
              <text class="timer-button__text">暂停</text>
            </view>

            <!-- 结束按钮 - 运行时也可以结束 -->
            <view class="timer-button timer-button--danger" @click="endTimer">
              <text class="timer-button__text">结束</text>
            </view>
          </template>
        </template>
      </view>
    </view>

    <!-- 番茄钟设置 (仅番茄钟模式显示) -->
    <view class="time-settings" v-if="timerMode === 'pomodoro'">
      <view class="time-settings__title-row">
        <text class="time-settings__title">番茄钟设置</text>
      </view>

      <!-- 专注时长选择 -->
      <view class="time-settings__subtitle">
        <text class="time-settings__subtitle-text">专注时长</text>
      </view>

      <view class="time-settings__options">
        <view class="time-option" :class="{'time-option--active': timerDuration === 25 * 60}" @click="setTimerDuration(25 * 60)">
          <text class="time-option__text">25分钟</text>
        </view>
        <view class="time-option" :class="{'time-option--active': timerDuration === 45 * 60}" @click="setTimerDuration(45 * 60)">
          <text class="time-option__text">45分钟</text>
        </view>
        <view class="time-option" :class="{'time-option--active': timerDuration === 60 * 60}" @click="setTimerDuration(60 * 60)">
          <text class="time-option__text">60分钟</text>
        </view>
      </view>

      <!-- 番茄钟个数 -->
      <view class="time-settings__subtitle" style="margin-top: 16px;">
        <text class="time-settings__subtitle-text">番茄钟个数</text>
        <view class="time-settings__edit" @click="setPomodoroCount">
          <text class="time-settings__edit-text">编辑</text>
        </view>
      </view>

      <view class="pomodoro-count">
        <view
          v-for="i in pomodoroCount"
          :key="i"
          class="pomodoro-icon"
          :class="{'pomodoro-icon--completed': i <= completedPomodoros, 'pomodoro-icon--current': i === currentPomodoro && isTimerRunning}"
        >
          <text class="pomodoro-icon__text">{{ i }}</text>
        </view>
      </view>

      <!-- 保存设置按钮 -->
      <view class="save-settings-button" @click="savePomodoroSettings">
        <text class="save-settings-button__text">保存设置</text>
      </view>

      <view class="pomodoro-status" v-if="isTimerRunning">
        <text class="pomodoro-status__text">正在进行第 {{ currentPomodoro }} 个番茄钟，已完成 {{ completedPomodoros }} 个</text>
      </view>
    </view>




  </view>
</template>

<script lang="ts">
import { ref, computed, reactive } from 'vue';
import { IRefs } from '../../utils/types';

export default {
  emits: ['navigate-to-ai', 'open-records'],
  data() {
    return {
      // 计时器状态
      timerMode: 'pomodoro' as string, // 'pomodoro'(番茄钟), 'countdown'(倒计时), 'stopwatch'(正计时)
      timerDuration: 25 * 60, // 默认25分钟
      remainingTime: 25 * 60,
      elapsedTime: 0,
      isTimerRunning: false,
      timerInterval: null as number | null,
      taskName: '', // 任务名称

      // 番茄钟相关状态
      pomodoroCount: 4, // 番茄钟个数
      currentPomodoro: 1, // 当前番茄钟序号
      completedPomodoros: 0, // 已完成番茄钟数

      // 专注统计
      focusCount: 0, // 专注次数
      totalFocusTime: 0, // 总专注时长（分钟）

      // 自习室模式
      isModeStudyRoom: false, // 是否为自习室模式
      studyRoomId: null, // 自习室ID
      studyRoomName: null, // 自习室名称

      // 计时器设置
      timerSettings: {
        defaultMode: 'pomodoro',
        pomodoroSettings: {
          workDuration: 25,
          shortBreakDuration: 5,
          longBreakDuration: 15,
          longBreakInterval: 4,
          autoStartBreaks: true,
          autoStartPomodoros: false
        },
        countdownSettings: {
          defaultDuration: 30
        },
        soundEnabled: true,
        notificationEnabled: true
      },

      // 加载状态
      loading: false
    }
  },
  computed: {
    // 格式化时间显示
    formattedTime() {
      let timeToFormat = this.timerMode === 'stopwatch' ? this.elapsedTime : this.remainingTime;
      const minutes = Math.floor(timeToFormat / 60);
      const seconds = timeToFormat % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },

    // 计时器标签文本
    timerLabel() {
      if (!this.isTimerRunning) {
        return '准备开始';
      }

      switch(this.timerMode) {
        case 'pomodoro': return '番茄钟';
        case 'countdown': return '倒计时中';
        case 'stopwatch': return '正计时';
        default: return '计时中';
      }
    },

    // 计时器圆圈样式
    timerCircleClass() {
      const baseClass = 'timer-circle';
      if (!this.isTimerRunning) return baseClass;

      // 根据不同模式返回不同样式
      switch(this.timerMode) {
        case 'pomodoro': return `${baseClass} timer-circle--pomodoro-active`;
        case 'countdown': return `${baseClass} timer-circle--countdown-active`;
        case 'stopwatch': return `${baseClass} timer-circle--stopwatch-active`;
        default: return `${baseClass} timer-circle--active`;
      }
    }
  },
  mounted() {
    // 加载计时器设置
    this.loadTimerSettings();
    // 加载专注统计
    this.loadFocusStats();
  },
  // 页面加载时处理URL参数
  onLoad(options) {
    console.log('TimeTimer 页面加载参数:', options);
    // 检查是否为自习室模式
    if (options.mode === 'studyroom' && options.roomId) {
      // 设置自习室模式
      this.isModeStudyRoom = true;
      this.studyRoomId = options.roomId;
      this.studyRoomName = options.roomName || '自习室';

      // 自动设置任务名称为自习室名称
      this.taskName = `在${this.studyRoomName}学习`;

      console.log(`进入自习室模式 - ID: ${this.studyRoomId}, 名称: ${this.studyRoomName}`);
    }
  },
  methods: {


    // 切换计时模式
    switchTimerMode(mode) {
      // 如果计时器正在运行，需要确认
      if (this.isTimerRunning) {
        uni.showModal({
          title: '提示',
          content: '切换模式将停止当前计时，确定要切换吗？',
          success: (res) => {
            if (res.confirm) {
              this.doSwitchTimerMode(mode);
            }
          }
        });
      } else {
        this.doSwitchTimerMode(mode);
      }
    },

    // 执行切换计时模式
    doSwitchTimerMode(mode) {
      this.timerMode = mode;
      this.resetTimer();
    },

    // 开始/暂停计时器
    toggleTimer() {
      if (this.isTimerRunning) {
        // 暂停计时器
        clearInterval(this.timerInterval);
        this.isTimerRunning = false;
      } else {
        // 开始前检查任务名称
        if (!this.taskName.trim()) {
          uni.showToast({
            title: '请先输入任务名称',
            icon: 'none',
            duration: 2000
          });
          return;
        }

        // 根据不同模式处理
        if (this.timerMode === 'stopwatch') {
          // 正计时模式
          this.isTimerRunning = true;
          this.timerInterval = setInterval(() => {
            this.elapsedTime++;
            this.remainingTime = this.elapsedTime; // 保持同步
          }, 1000) as unknown as number;
        } else if (this.timerMode === 'countdown') {
          // 倒计时模式
          if (this.remainingTime <= 0) {
            this.resetTimer();
          }

          this.isTimerRunning = true;
          this.timerInterval = setInterval(() => {
            if (this.remainingTime > 0) {
              this.remainingTime--;
              this.elapsedTime++;
            } else {
              // 倒计时结束
              this.isTimerRunning = false;
              clearInterval(this.timerInterval as number);
              this.playAlarm();
            }
          }, 1000) as unknown as number;
        } else if (this.timerMode === 'pomodoro') {
          // 番茄钟模式
          if (this.remainingTime <= 0) {
            this.resetTimer();
          }

          this.isTimerRunning = true;
          this.timerInterval = setInterval(() => {
            if (this.remainingTime > 0) {
              this.remainingTime--;
              this.elapsedTime++;
            } else {
              // 当前番茄钟完成
              this.completedPomodoros++;
              this.playAlarm();

              if (this.completedPomodoros < this.pomodoroCount) {
                // 当前番茄钟完成但还有下一个
                uni.showToast({
                  title: `第${this.currentPomodoro}个番茄钟已完成，开始休息`,
                  icon: 'none',
                  duration: 2000
                });

                // 休息5分钟
                this.currentPomodoro++;
                this.remainingTime = 5 * 60; // 5分钟休息

                // 记录专注
                this.addFocusRecord();
              } else {
                // 所有番茄钟完成
                this.isTimerRunning = false;
                clearInterval(this.timerInterval as number);

                uni.showToast({
                  title: `恭喜完成全部${this.pomodoroCount}个番茄钟`,
                  icon: 'success',
                  duration: 2000
                });

                // 记录最后一个
                this.addFocusRecord();

                // 重置状态
                this.completedPomodoros = 0;
                this.currentPomodoro = 1;
              }
            }
          }, 1000) as unknown as number;
        }
      }
    },

    // ?????
    resetTimer() {
      if (this.timerInterval !== null) {
        clearInterval(this.timerInterval as number);
      }
      this.isTimerRunning = false;
      this.elapsedTime = 0;

      // ??????????
      if (this.timerMode === 'stopwatch') {
        this.remainingTime = 0;
      } else if (this.timerMode === 'countdown') {
        this.remainingTime = this.timerDuration;
      } else if (this.timerMode === 'pomodoro') {
        this.remainingTime = this.timerDuration;
        this.completedPomodoros = 0;
        this.currentPomodoro = 1;
      }
    },

    // ???????
    setTimerDuration(duration) {
      this.timerDuration = duration;
      this.resetTimer();
    },

    // ??????
    setPomodoroCount() {
      uni.showModal({
        title: '??????',
        content: '',
        editable: true,
        placeholderText: '???????????',
        success: (res) => {
          if (res.confirm && res.content) {
            const count = parseInt(res.content);
            if (!isNaN(count) && count > 0) {
              this.pomodoroCount = count;
              uni.showToast({
                title: `???${count}????`,
                icon: 'none'
              });
            }
          }
        }
      });
    },



    // ????????????????
    setCustomCountdownTime() {
      if (this.timerMode !== 'countdown') return;

      uni.showModal({
        title: '?????',
        content: '',
        editable: true,
        placeholderText: '??????',
        success: (res) => {
          if (res.confirm && res.content) {
            const minutes = parseInt(res.content);
            if (!isNaN(minutes) && minutes > 0) {
              this.timerDuration = minutes * 60;
              this.resetTimer();
            }
          }
        }
      });
    },

    // ?????
    playAlarm() {
      uni.showToast({
        title: '???????',
        icon: 'success'
      });
    },

    // ??????
    addFocusRecord() {
      // ????????1??????
      if (this.elapsedTime < 60) {
        console.log('??????????');
        return;
      }

      // ??????
      uni.showLoading({
        title: '???...'
      });

      // ??API
      import('../../utils/api/time.js').then(api => {
        const { createFocusRecord } = api;

        // ??????
        const now = new Date();
        const startTime = new Date(now.getTime() - this.elapsedTime * 1000);

        // ?????????????
        const taskName = this.taskName.trim() || '????';

        // ???????????API????????FocusRecordDTO????
        const recordData = {
          task: taskName,
          duration: Math.floor(this.elapsedTime / 60), // ?????
          mode: this.timerMode,
          startTime: this.formatDateTime(startTime),
          endTime: this.formatDateTime(now),
          isCompleted: true,
          planId: null,
          studyRoomId: null,
          studyRoomName: null,
          recordType: 0
        };

        // ???????????????
        if (this.isModeStudyRoom && this.studyRoomId) {
          recordData.studyRoomId = this.studyRoomId;
          recordData.studyRoomName = this.studyRoomName || '';
          recordData.recordType = 1; // ????????
        }

        console.log('????????:', recordData);

        // ??API????
        createFocusRecord(recordData)
          .then(res => {
            uni.hideLoading();
            if (res.code === 200 && res.data) {
              console.log('???????:', res.data);

              // ??????
              this.focusCount++;
              this.totalFocusTime += Math.floor(this.elapsedTime / 60);

              // ????
              uni.showToast({
                title: '?????',
                icon: 'success'
              });
            } else {
              console.error('????????:', res.message || '????');
              uni.showToast({
                title: '??????',
                icon: 'none'
              });
            }
          })
          .catch(err => {
            uni.hideLoading();
            console.error('????????:', err);
            uni.showToast({
              title: '??????',
              icon: 'none'
            });
          });
      }).catch(err => {
        uni.hideLoading();
        console.error('??API??:', err);
        uni.showToast({
          title: '??API??',
          icon: 'none'
        });
      });
    },

    // ??????????
    endTimer() {
      console.log('结束计时器被调用:', {
        isTimerRunning: this.isTimerRunning,
        elapsedTime: this.elapsedTime,
        timerMode: this.timerMode,
        remainingTime: this.remainingTime
      });

      // 如果计时器正在运行，先停止
      if (this.isTimerRunning) {
        clearInterval(this.timerInterval as number);
        this.isTimerRunning = false;
      }

      // 如果计时器还没开始或者刚刚重置，直接返回
      if (this.elapsedTime === 0 && this.remainingTime === this.timerDuration) {
        console.log('计时器尚未开始');
        return;
      }

      // 如果专注时间太短（小于1分钟），不记录
      if (this.elapsedTime < 60) {
        console.log('专注时间太短，不记录专注记录');
        uni.showToast({
          title: '专注时间太短',
          icon: 'none'
        });
        this.resetTimer();
        return;
      }

      // 如果是正计时模式，直接询问是否保存
      if (this.timerMode === 'stopwatch') {
        uni.showModal({
          title: '提示',
          content: '确定要结束计时吗？',
          success: (res) => {
            if (res.confirm) {
              this.saveTimerRecord();
            } else {
              // 取消则重置计时器
              this.resetTimer();
            }
          }
        });
      } else {
        // 倒计时或番茄钟
        uni.showModal({
          title: '提示',
          content: '确定要结束计时吗？',
          success: (res) => {
            if (res.confirm) {
              // 保存
              this.saveTimerRecord();
            } else {
              // 继续计时
              this.toggleTimer();
            }
          }
        });
      }
    },

    // 保存记录
    saveTimerRecord() {
      // 显示加载
      uni.showLoading({
        title: '保存中...'
      });

      // 导入API
      import('../../utils/api/time.js').then(api => {
        const { createFocusRecord } = api;

        // 计算时间
        const now = new Date();
        const startTime = new Date(now.getTime() - this.elapsedTime * 1000);

        // 如果没有任务名称则使用默认值
        const taskName = this.taskName.trim() || '未命名任务';

        // 构建记录数据对象，根据API要求的FocusRecordDTO构建
        const recordData = {
          task: taskName,
          duration: Math.floor(this.elapsedTime / 60), // 转换为分钟
          mode: this.timerMode,
          startTime: this.formatDateTime(startTime),
          endTime: this.formatDateTime(now),
          isCompleted: true,
          planId: null,
          studyRoomId: null,
          studyRoomName: null,
          recordType: 0
        };

        // 如果是在自习室中，添加自习室信息
        if (this.isModeStudyRoom && this.studyRoomId) {
          recordData.studyRoomId = this.studyRoomId;
          recordData.studyRoomName = this.studyRoomName || '';
          recordData.recordType = 1; // 自习室专注记录
        }

        console.log('保存专注记录:', recordData);

        // 调用createFocusRecord API保存记录
        createFocusRecord(recordData)
          .then(res => {
            uni.hideLoading();
            if (res.code === 200 && res.data) {
              console.log('保存专注记录成功:', res.data);

              // 更新统计
              this.focusCount++;
              this.totalFocusTime += Math.floor(this.elapsedTime / 60);

              // 提示
              uni.showToast({
                title: '记录已保存',
                icon: 'success'
              });
            } else {
              console.error('保存专注记录失败:', res.message || '未知错误');
              uni.showToast({
                title: '保存记录失败',
                icon: 'none'
              });
            }
          })
          .catch(err => {
            uni.hideLoading();
            console.error('保存专注记录出错:', err);
            uni.showToast({
              title: '保存记录失败',
              icon: 'none'
            });
          })
          .finally(() => {
            // 重置计时器
            this.resetTimer();
          });
      }).catch(err => {
        uni.hideLoading();
        console.error('导入API失败:', err);
        uni.showToast({
          title: '导入API失败',
          icon: 'none'
        });
        // 重置计时器
        this.resetTimer();
      });
    },

    // ???????? yyyy-MM-dd HH:mm:ss
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // ???????
    loadTimerSettings() {
      // 导入API
      import('../../utils/api/time.js').then(api => {
        const { getTimerSettings } = api;

        // 获取计时器设置
        getTimerSettings()
          .then(res => {
            if (res.code === 200 && res.data) {
              // 解析数据
              const data = res.data;
              console.log('获取计时器设置成功:', JSON.stringify(data));

              // 设置模式
              if (data.defaultMode) {
                this.timerMode = data.defaultMode;
              }

              // 设置番茄钟 - 专注时长
              if (data.pomodoroSettings && data.pomodoroSettings.workDuration) {
                this.timerDuration = data.pomodoroSettings.workDuration * 60;
                this.remainingTime = this.timerDuration;
              }

              // 设置番茄钟 - 番茄钟个数
              if (data.pomodoroSettings && data.pomodoroSettings.longBreakInterval) {
                this.pomodoroCount = data.pomodoroSettings.longBreakInterval;
              }

              // 保存完整设置 - 用于后续保存和恢复
              this.timerSettings = {
                defaultMode: data.defaultMode,
                pomodoroSettings: data.pomodoroSettings,
                countdownSettings: data.countdownSettings,
                soundEnabled: data.soundEnabled,
                notificationEnabled: data.notificationEnabled
              };
            } else {
              // 如果获取失败，使用默认设置
              this.timerSettings = {
                defaultMode: 'pomodoro',
                pomodoroSettings: {
                  workDuration: 25,
                  shortBreakDuration: 5,
                  longBreakDuration: 15,
                  longBreakInterval: 4,
                  autoStartBreaks: true,
                  autoStartPomodoros: false
                },
                countdownSettings: {
                  defaultDuration: 30
                },
                soundEnabled: true,
                notificationEnabled: true
              };

              // 设置默认值
              this.timerMode = 'pomodoro';
              this.timerDuration = 25 * 60;
              this.remainingTime = this.timerDuration;
              this.pomodoroCount = 4;

              // 保存默认设置到服务器
              this.savePomodoroSettings();
            }
          })
          .catch(err => {
            console.error('获取计时器设置失败:', err);

            // 如果获取失败，使用默认设置
            this.timerSettings = {
              defaultMode: 'pomodoro',
              pomodoroSettings: {
                workDuration: 25,
                shortBreakDuration: 5,
                longBreakDuration: 15,
                longBreakInterval: 4,
                autoStartBreaks: true,
                autoStartPomodoros: false
              },
              countdownSettings: {
                defaultDuration: 30
              },
              soundEnabled: true,
              notificationEnabled: true
            };

            // 设置默认值
            this.timerMode = 'pomodoro';
            this.timerDuration = 25 * 60;
            this.remainingTime = this.timerDuration;
            this.pomodoroCount = 4;
          });
      }).catch(err => {
        console.error('导入API失败:', err);

        // 如果导入失败，使用默认设置
        this.timerSettings = {
          defaultMode: 'pomodoro',
          pomodoroSettings: {
            workDuration: 25,
            shortBreakDuration: 5,
            longBreakDuration: 15,
            longBreakInterval: 4,
            autoStartBreaks: true,
            autoStartPomodoros: false
          },
          countdownSettings: {
            defaultDuration: 30
          },
          soundEnabled: true,
          notificationEnabled: true
        };

        // 设置默认值
        this.timerMode = 'pomodoro';
        this.timerDuration = 25 * 60;
        this.remainingTime = this.timerDuration;
        this.pomodoroCount = 4;
      });
    },

    // 保存设置
    savePomodoroSettings() {
      // 显示加载
      uni.showLoading({
        title: '保存中...'
      });

      // 导入API
      import('../../utils/api/time.js').then(api => {
        const { saveTimerSettings } = api;

        // 构建设置数据 - 根据API接口要求构建
        const settingsData = {
          defaultMode: this.timerMode,
          pomodoroSettings: {
            workDuration: Math.floor(this.timerDuration / 60),
            shortBreakDuration: 5,
            longBreakDuration: 15,
            longBreakInterval: this.pomodoroCount,
            autoStartBreaks: true,
            autoStartPomodoros: false
          },
          countdownSettings: {
            defaultDuration: 30
          },
          soundEnabled: true,
          notificationEnabled: true
        };

        console.log('保存计时器设置:', JSON.stringify(settingsData));

        // 调用API保存设置
        saveTimerSettings(settingsData)
          .then(res => {
            uni.hideLoading();
            if (res.code === 200) {
              uni.showToast({
                title: '设置已保存',
                icon: 'success'
              });

              // 更新本地
              this.timerSettings = {
                defaultMode: settingsData.defaultMode,
                pomodoroSettings: {
                  workDuration: settingsData.pomodoroSettings.workDuration,
                  shortBreakDuration: settingsData.pomodoroSettings.shortBreakDuration,
                  longBreakDuration: settingsData.pomodoroSettings.longBreakDuration,
                  longBreakInterval: settingsData.pomodoroSettings.longBreakInterval,
                  autoStartBreaks: settingsData.pomodoroSettings.autoStartBreaks,
                  autoStartPomodoros: settingsData.pomodoroSettings.autoStartPomodoros
                },
                countdownSettings: {
                  defaultDuration: settingsData.countdownSettings.defaultDuration
                },
                soundEnabled: settingsData.soundEnabled,
                notificationEnabled: settingsData.notificationEnabled
              };
            } else {
              uni.showToast({
                title: '保存设置失败: ' + (res.message || '未知错误'),
                icon: 'none'
              });
              console.error('保存设置失败响应:', res);
            }
          })
          .catch(err => {
            uni.hideLoading();
            console.error('保存设置出错:', err);
            uni.showToast({
              title: '保存设置失败: ' + (err.message || '未知错误'),
              icon: 'none'
            });
          });
      }).catch(err => {
        uni.hideLoading();
        console.error('导入API失败:', err);
        uni.showToast({
          title: '导入API失败',
          icon: 'none'
        });
      });
    },

    // 加载专注统计
    loadFocusStats() {
      // 导入API
      import('../../utils/api/time.js').then(api => {
        const { getFocusStats } = api;

        // 获取今日专注统计
        getFocusStats('day')
          .then(res => {
            console.log('获取专注统计成功:', res);
            if (res.code === 200 && res.data) {
              // 更新统计
              this.focusCount = res.data.totalSessions || 0;
              this.totalFocusTime = res.data.totalDuration || 0;
            }
          })
          .catch(err => {
            console.error('获取专注统计失败:', err);
          });
      }).catch(err => {
        console.error('导入API失败:', err);
      });
    },

    // 打开专注记录
    openFocusRecords() {
      // 触发父组件的事件来切换到记录页面
      this.$emit('open-records');
    },
  },
  onUnload() {
    // ??????????
    if (this.timerInterval !== null) {
      clearInterval(this.timerInterval as number);
    }
  }
}
</script>

<style>
/* ??????? */
.timer-mode-selector {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 14px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.05);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

/* ???? */
.focus-section {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 14px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.05);
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.timer-mode-selector::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: linear-gradient(to right, #5B7FFF, #80B8F5);
  border-radius: 14px 14px 0 0;
}

.timer-mode-title-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
}

.timer-mode-title {
  font-size: 16px;
  font-weight: 600;
  color: #2E3A59;
  letter-spacing: 0.2px;
}

.timer-mode-options {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 3px;
  background-color: rgba(240, 243, 250, 0.6);
  border-radius: 10px;
}

.timer-mode-option {
  flex: 1;
  padding: 10px 0;
  border-radius: 8px;
  margin: 0 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.timer-mode-option--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.timer-mode-option__text {
  font-size: 14px;
  font-weight: 500;
  color: #5E6C84;
  transition: all 0.3s ease;
}

.timer-mode-option--active .timer-mode-option__text {
  color: #FFFFFF;
  font-weight: 600;
}

/* ??? */
.timer-card {
  background-color: #FFFFFF;
  border-radius: 14px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  position: relative;
  overflow: hidden;
}

.timer-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #5B7FFF, #80B8F5);
}

.timer-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 8px solid #EEF2FF;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  box-shadow: 0 6px 20px rgba(31, 60, 136, 0.08);
  transition: all 0.3s ease;
}

.timer-circle--active {
  border-color: #5B7FFF;
  box-shadow: 0 8px 24px rgba(91, 127, 255, 0.15);
}

.timer-circle--pomodoro-active {
  border-color: #5B7FFF;
  box-shadow: 0 8px 24px rgba(91, 127, 255, 0.15);
}

.timer-circle--countdown-active {
  border-color: #F79F77;
  box-shadow: 0 8px 24px rgba(247, 159, 119, 0.15);
}

.timer-circle--stopwatch-active {
  border-color: #5B7FFF;
  box-shadow: 0 8px 24px rgba(91, 127, 255, 0.15);
}

.timer-time {
  font-size: 42px;
  font-weight: 700;
  color: #2E3A59;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.timer-label {
  font-size: 16px;
  color: #5E6C84;
  font-weight: 500;
}

.timer-controls {
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.timer-button {
  padding: 12px 24px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 12px rgba(31, 60, 136, 0.1);
}

.timer-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 6px rgba(31, 60, 136, 0.15);
}

.timer-button--primary {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
}

.timer-button--secondary {
  background-color: #EEF2FF;
}

.timer-button--secondary .timer-button__text {
  color: #5B7FFF;
}

.timer-button--warning {
  background: linear-gradient(135deg, #FF9800, #FFC107);
}

.timer-button--danger {
  background: linear-gradient(135deg, #FF5252, #FF7676);
}

.timer-button__text {
  font-size: 15px;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.2px;
}

/* ?????? */
.task-input-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  padding: 0;
}

.task-input {
  width: 80%; /* ???????? */
  height: 42px;
  border: 1px solid rgba(91, 127, 255, 0.2);
  border-radius: 10px;
  padding: 0 14px;
  font-size: 14px;
  color: #2E3A59;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
  text-align: center;
}

.task-input:focus {
  border-color: #5B7FFF;
  box-shadow: 0 0 0 2px rgba(91, 127, 255, 0.1);
}

/* ?????? */
.task-name-display {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 15px;
  background-color: #EEF2FF;
  border-radius: 10px;
  border-left: 3px solid #5B7FFF;
}

.task-name-text {
  font-size: 15px;
  font-weight: 600;
  color: #2E3A59;
  text-align: center;
}

/* ???? */
.time-settings, .focus-section, .focus-history {
  background-color: #FFFFFF;
  border-radius: 14px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  position: relative;
  overflow: hidden;
}

.time-settings::before, .focus-section::before, .focus-history::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #5B7FFF, #80B8F5);
}

.time-settings__title-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(240, 243, 250, 0.8);
}

.time-settings__title {
  font-size: 16px;
  font-weight: 600;
  color: #2E3A59;
  letter-spacing: 0.2px;
}

.time-settings__more {
  font-size: 14px;
  color: #5B7FFF;
  font-weight: 500;
}

.time-settings__subtitle {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.time-settings__subtitle-text {
  font-size: 14px;
  color: #5E6C84;
  font-weight: 500;
}

.time-settings__edit {
  padding: 4px 10px;
  background-color: #EEF2FF;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.time-settings__edit:active {
  transform: scale(0.95);
}

.time-settings__edit-text {
  font-size: 12px;
  color: #5B7FFF;
  font-weight: 500;
}

.time-settings__options {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: rgba(240, 243, 250, 0.6);
  border-radius: 10px;
  padding: 3px;
}

/* ????? */
.pomodoro-count {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 12px;
  gap: 8px;
}

.pomodoro-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #EEF2FF;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 6px rgba(31, 60, 136, 0.08);
  transition: all 0.3s ease;
}

.pomodoro-icon--completed {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.pomodoro-icon--current {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.15);
  animation: pulse 1.5s infinite;
}

.pomodoro-icon__text {
  font-size: 14px;
  font-weight: 600;
  color: #5E6C84;
}

.pomodoro-icon--completed .pomodoro-icon__text,
.pomodoro-icon--current .pomodoro-icon__text {
  color: #FFFFFF;
}

.pomodoro-status {
  margin-top: 10px;
  background-color: #EEF2FF;
  padding: 8px 12px;
  border-radius: 10px;
}

.pomodoro-status__text {
  font-size: 14px;
  color: #5E6C84;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.time-option {
  flex: 1;
  padding: 10px 0;
  border-radius: 8px;
  margin: 0 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.time-option--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.time-option__text {
  font-size: 14px;
  font-weight: 500;
  color: #5E6C84;
  transition: all 0.3s ease;
}

.time-option--active .time-option__text {
  color: #FFFFFF;
  font-weight: 600;
}





/* ?????? */
.save-settings-button {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  padding: 10px 0;
  border-radius: 10px;
  margin-top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
  transition: all 0.3s ease;
}

.save-settings-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 6px rgba(91, 127, 255, 0.2);
}

.save-settings-button__text {
  font-size: 15px;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.2px;
}

/* ???? */
.focus-stats {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  gap: 10px;
}

.focus-stat {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #EEF2FF;
  padding: 14px 10px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.focus-stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(91, 127, 255, 0.1);
}

.focus-stat__value {
  font-size: 24px;
  font-weight: 700;
  color: #5B7FFF;
  margin-bottom: 6px;
}

.focus-stat__label {
  font-size: 13px;
  color: #5E6C84;
  font-weight: 500;
}
</style>
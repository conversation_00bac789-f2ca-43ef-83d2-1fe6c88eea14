<template>
  <view class="change-password-container">
    <!-- 导航栏 -->
    <sla-navbar
      title="修改密码"
      @back="navigateBack"
    />

    <sla-form class="change-password-form" :model="formData" :rules="rules" ref="changePasswordForm">
      <sla-input
        v-model="formData.oldPassword"
        label="原密码"
        placeholder="请输入原密码"
        type="password"
        required
        :error="getFieldError('oldPassword')"
      />

      <sla-input
        v-model="formData.newPassword"
        label="新密码"
        placeholder="请输入新密码"
        type="password"
        required
        :error="getFieldError('newPassword')"
      />

      <sla-input
        v-model="formData.confirmPassword"
        label="确认密码"
        placeholder="请再次输入新密码"
        type="password"
        required
        :error="getFieldError('confirmPassword')"
      />

      <view class="change-password-tips">
        <text class="change-password-tips__title">密码要求：</text>
        <text class="change-password-tips__item">• 长度为6-20位字符</text>
        <text class="change-password-tips__item">• 必须包含至少一个字母和一个数字</text>
        <text class="change-password-tips__item">• 不能与原密码相同</text>
      </view>

      <sla-button
        text="确认修改"
        type="primary"
        size="large"
        :loading="loading"
        @click="handleChangePassword"
        class="change-password-button"
      />
    </sla-form>
  </view>
</template>

<script lang="ts">
import { ref, computed, reactive } from 'vue';
import { IRefs } from '../../utils/types';
import SlaButton from '../../components/user/SlaButton.uvue';
import SlaInput from '../../components/user/SlaInput.uvue';
import SlaForm from '../../components/user/SlaForm.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import { changePassword } from '../../utils/api/user.js';

export default {
  components: {
    SlaButton,
    SlaInput,
    SlaForm,
    SlaNavbar
  },
  data() {
    return {
      // 表单数据
      formData: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      // 验证规则
      rules: {
        oldPassword: [
          { required: true, message: '请输入原密码' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码' },
          { min: 6, max: 20, message: '密码长度为6-20位字符' },
          {
            pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/,
            message: '密码必须包含至少一个字母和一个数字'
          },
          {
            validator: (value) => {
              return value !== this.formData.oldPassword ? true : '新密码不能与原密码相同';
            }
          }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码' },
          {
            validator: (value) => {
              return value === this.formData.newPassword ? true : '两次输入的密码不一致';
            }
          }
        ]
      },
      // 加载状态
      loading: false
    };
  },
  methods: {
    // 获取字段错误信息
    getFieldError(field) {
      return this.$refs.changePasswordForm?.getFieldError ? this.$refs.changePasswordForm.getFieldError(field) : '';
    },

    // 修改密码处理
    handleChangePassword() {
      // 表单验证
      if (this.$refs.changePasswordForm && typeof this.$refs.changePasswordForm.validate === 'function') {
        (this.$refs.changePasswordForm as unknown as IRefs['changePasswordForm']).validate().then(({ valid, errors }) => {
          if (!valid) {
            const firstError = Object.values(errors)[0] as string;
            uni.showToast({
              title: firstError,
              icon: 'none'
            });
            return;
          }

          this.loading = true;

          // 构建请求参数
          const changeParams = {
            oldPassword: this.formData.oldPassword,
            newPassword: this.formData.newPassword
          };

          // 调用修改密码API
          changePassword(changeParams).then(() => {
            uni.showToast({
              title: '密码修改成功',
              icon: 'success'
            });

            // 重置表单
            this.formData = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            };

            // 延迟跳转
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }).catch(err => {
            uni.showToast({
              title: err.message || '修改失败',
              icon: 'none'
            });
          }).finally(() => {
            this.loading = false;
          });
        });
      } else {
        uni.showToast({
          title: '表单验证失败',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style>
.change-password-container {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding: 20px;
}

.change-password-form {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.change-password-tips {
  margin: 20px 0;
  padding: 16px;
  background-color: #F8F9FA;
  border-radius: 8px;
}

.change-password-tips__title {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.change-password-tips__item {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4px;
  display: block;
}

.change-password-button {
  width: 100%;
  margin-top: 24px;
}
</style>

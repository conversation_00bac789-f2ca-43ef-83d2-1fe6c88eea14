<template>
  <view class="sla-avatar" :class="[`sla-avatar--${size}`]" @click="handleClick">
    <image 
      class="sla-avatar__image" 
      :src="src || defaultAvatar" 
      :mode="mode"
    ></image>
    <view class="sla-avatar__edit" v-if="editable">
      <text class="sla-avatar__edit-icon">âď¸</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SlaAvatar',
  props: {
    // ĺ¤´ĺĺžçĺ°ĺ
    src: {
      type: String,
      default: ''
    },
    // éťčŽ¤ĺ¤´ĺĺžçĺ°ĺ
    defaultAvatar: {
      type: String,
      default: '/static/images/default-avatar.png'
    },
    // ĺ¤´ĺĺ¤§ĺ°ďźlarge, medium, small
    size: {
      type: String,
      default: 'medium'
    },
    // ĺžçčŁĺŞć¨Ąĺź
    mode: {
      type: String,
      default: 'aspectFill'
    },
    // ćŻĺŚĺŻçźčž?    editable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // ĺ¤ççšĺťäşäťś
    handleClick() {
      if (this.editable) {
        this.chooseImage();
      }
      this.$emit('click');
    },
    // éćŠĺžç
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.$emit('change', tempFilePath);
        }
      });
    }
  }
}
</script>

<style>
.sla-avatar {
  position: relative;
  border-radius: 999px;
  overflow: hidden;
}

.sla-avatar--large {
  width: 96px;
  height: 96px;
}

.sla-avatar--medium {
  width: 64px;
  height: 64px;
}

.sla-avatar--small {
  width: 40px;
  height: 40px;
}

.sla-avatar__image {
  width: 100%;
  height: 100%;
}

.sla-avatar__edit {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sla-avatar__edit-icon {
  font-size: 14px;
  color: #FFFFFF;
}
</style>


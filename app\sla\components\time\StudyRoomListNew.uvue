<template>
  <view class="study-room-container">
    <!-- 内容区域 -->
    <scroll-view
      class="content-scroll"
      scroll-y
      @scrolltolower="loadMoreRooms"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refreshRooms"
    >
      <!-- 自习室列表区域 -->
      <view class="section all-rooms-section">
        <!-- 搜索和刷新区域 -->
        <view class="search-refresh-container">
          <view class="search-box">
            <text class="search-icon">🔍</text>
            <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索自习室" @confirm="searchRooms" />
            <text v-if="searchKeyword" class="clear-icon" @click="clearSearch">×</text>
          </view>
          <view class="refresh-btn" @click="refreshRooms">
            <text class="refresh-icon">↻</text>
          </view>
        </view>

        <!-- 自习室内容 -->
        <view class="section-content">
          <!-- 加载中 -->
          <uni-load-more v-if="loading && !refreshing" status="loading"></uni-load-more>

          <!-- 空状态 -->
          <view class="empty-state simple-empty" v-if="!loading && studyRooms.length === 0">
            <image class="empty-image" src="../../static/images/study-empty.svg" mode="aspectFit"></image>
            <text class="empty-text">暂无自习室</text>
          </view>

          <!-- 自习室列表 - 网格布局 -->
          <view v-if="studyRooms.length > 0" class="room-grid">
            <view
              v-for="room in studyRooms"
              :key="room.roomId"
              class="room-card"
              @click="enterRoomDetail(room)"
            >
              <!-- 左侧人数显示 -->
              <view class="room-card-left">
                <text class="user-icon">👤</text>
                <text class="capacity-text">{{ room.currentUsers || room.currentMembers || 0 }}/{{ room.capacity || 8 }}</text>
              </view>

              <!-- 中间内容区 -->
              <view class="room-card-content">
                <text class="room-name">{{ room.name }}</text>

                <!-- 标签区域 -->
                <view class="room-tags" v-if="room.tags && room.tags.length > 0">
                  <text class="room-tag" v-for="(tag, tagIndex) in room.tags.slice(0, 2)" :key="tagIndex">{{ tag }}</text>
                  <text class="more-tags" v-if="room.tags.length > 2">+{{ room.tags.length - 2 }}</text>
                </view>
              </view>

              <!-- 加入按钮 -->
              <view
                class="join-btn"
                :class="{ 'joined': room.isJoined }"
                @click.stop="joinRoom(room)"
              >
                <text class="join-text">{{ getJoinButtonText(room) }}</text>
              </view>
            </view>
          </view>

          <!-- 加载更多 -->
          <uni-load-more :status="loadMoreStatus"></uni-load-more>
        </view>
      </view>
    </scroll-view>

    <!-- 创建自习室悬浮按钮 -->
    <view class="floating-create-btn" @click="navigateToCreateRoom">
      <text class="create-icon">+</text>
    </view>

    <!-- 密码输入弹窗 -->
    <uni-popup ref="passwordPopup" type="center">
      <view class="password-popup">
        <view class="popup-header">
          <text class="popup-title">输入自习室密码</text>
          <text class="popup-subtitle">请输入密码加入自习室</text>
        </view>
        <view class="popup-content">
          <input
            class="password-input"
            type="password"
            v-model="roomPassword"
            placeholder="请输入密码"
            focus
          />
          <view class="password-btns">
            <button class="cancel-btn" @click="closePasswordPopup">取消</button>
            <button class="confirm-btn" @click="confirmJoinRoom">确认</button>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import UniPopup from '../../uni_modules/uni-popup/components/uni-popup/uni-popup.uvue';
import UniLoadMore from '../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.uvue';
// 导入自习室API
import { getStudyRooms, createStudyRoom, joinStudyRoom, getStudyRoomDetail } from '../../utils/api/time.js';

export default {
  components: {
    UniPopup,
    UniLoadMore
  },
  emits: ['select-room'],
  data() {
    return {
      // 搜索相关
      searchKeyword: '',

      // 房间列表
      studyRooms: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      refreshing: false,
      loadMoreStatus: 'more', // more, loading, noMore

      // 加入房间相关
      selectedRoom: null,
      roomPassword: '',
      refreshDebounceTimer: null
    };
  },
  created() {
    // 加载自习室列表
    this.loadRooms();

    // 监听刷新事件 - 使用两个事件名称，确保兼容性
    uni.$on('refreshStudyRoomList', this.handleRefreshEvent);
    uni.$on('studyRoomListRefresh', this.handleRefreshEvent);

    // 监听自习室解散事件
    uni.$on('studyRoomDisbanded', this.handleRoomDisbanded);
  },
  onShow() {
    // 检查是否需要刷新列表
    try {
      const needRefresh = uni.getStorageSync('needRefreshStudyRoomList');
      if (needRefresh) {
        // 清除标记
        uni.removeStorageSync('needRefreshStudyRoomList');
        // 刷新列表
        this.refreshRooms();
      }
    } catch (e) {
      console.error('检查刷新标记失败:', e);
    }
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('refreshStudyRoomList', this.handleRefreshEvent);
    uni.$off('studyRoomListRefresh', this.handleRefreshEvent);
    uni.$off('studyRoomDisbanded', this.handleRoomDisbanded);
  },
  methods: {
    // 加载自习室列表
    loadRooms(reset = false) {
      if (reset) {
        this.pageNum = 1;
        this.studyRooms = [];
      }

      if (this.loading) {
        console.log('已有加载操作正在进行，跳过');
        return;
      }

      this.loading = true;

      // 确保显示加载状态
      uni.showLoading({
        title: '加载中...',
        mask: true
      });

      // 使用封装好的API方法
      getStudyRooms({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            keyword: this.searchKeyword
      })
        .then(response => {
                const data = response.data;
                this.total = data.total;

                // 处理后端返回的数据，确保属性名称一致
                const processedRooms = data.list.map(room => {
                  // 确保 currentMembers 属性存在
                  if (room.currentUsers !== undefined && room.currentMembers === undefined) {
                    room.currentMembers = room.currentUsers;
                  }
                  return room;
                });

                if (reset) {
                  this.studyRooms = processedRooms;
                } else {
                  this.studyRooms = [...this.studyRooms, ...processedRooms];
                }

                // 更新加载更多状态
                if (this.studyRooms.length >= this.total) {
                  this.loadMoreStatus = 'noMore';
                } else {
                  this.loadMoreStatus = 'more';
                }
        })
        .catch(error => {
          console.error('获取自习室列表失败:', error);
          uni.showToast({
            title: '获取自习室列表失败',
            icon: 'none'
          });
        })
        .finally(() => {
            this.loading = false;
            this.refreshing = false;
            // 确保隐藏加载状态
            uni.hideLoading();
        });
    },

    // 搜索自习室
    searchRooms() {
      this.loadRooms(true);
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.loadRooms(true);
    },

    // 加载更多自习室
    loadMoreRooms() {
      if (this.loadMoreStatus === 'noMore' || this.loading) return;

      this.pageNum++;
      this.loadMoreStatus = 'loading';
      this.loadRooms();
    },

    // 处理刷新事件，防止循环调用
    handleRefreshEvent() {
      console.log('收到刷新自习室列表事件');
      // 使用防抖处理，避免短时间内多次刷新
      if (this.refreshDebounceTimer) {
        clearTimeout(this.refreshDebounceTimer);
      }

      this.refreshDebounceTimer = setTimeout(() => {
        if (!this.loading && !this.refreshing) {
          console.log('执行列表刷新');
          this.refreshRooms();
        } else {
          console.log('列表正在加载中，跳过刷新');
        }
      }, 300);
    },

    // 刷新自习室列表
    refreshRooms() {
      console.log('刷新自习室列表');
      if (this.loading || this.refreshing) {
        console.log('列表正在刷新中，跳过重复刷新');
        return;
      }
      this.loadRooms(true);
    },

    // 跳转到创建自习室页面
    navigateToCreateRoom() {
      uni.navigateTo({
        url: '/pages/time/TimeStudyRoomCreate'
      });
    },

    // 进入自习室详情页
    enterRoomDetail(room) {
      // 如果已加入，直接进入详情页
      if (room.isJoined) {
        uni.navigateTo({
          url: '/pages/time/TimeStudyRoomDetail?roomId=' + room.roomId
        });
      } else {
        // 未加入，先尝试加入
        this.joinRoom(room);
      }
    },

    // 选择自习室
    selectRoom(room) {
      this.$emit('select-room', room);
    },

    // 加入自习室
    joinRoom(room) {
      if (room.isJoined) {
        // 已加入，直接进入详情页
        uni.navigateTo({
          url: '/pages/time/TimeStudyRoomDetail?roomId=' + room.roomId
        });
        return;
      }

      // 非公开自习室需要输入密码
      if (!room.isPublic) {
        this.selectedRoom = room;
        this.roomPassword = '';
        this.$refs.passwordPopup.open();
        return;
      }

      // 公开自习室直接加入
      this.doJoinRoom(room.roomId);
    },

    // 确认加入自习室（带密码）
    confirmJoinRoom() {
      if (!this.roomPassword) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
        return;
      }

      if (this.selectedRoom) {
        this.doJoinRoom(this.selectedRoom.roomId, this.roomPassword);
      }
      this.closePasswordPopup();
    },

    // 关闭密码弹窗
    closePasswordPopup() {
      this.$refs.passwordPopup.close();
      this.selectedRoom = null;
      this.roomPassword = '';
    },

    // 执行加入自习室
    doJoinRoom(roomId, password = '') {
      uni.showLoading({
        title: '加入中...'
      });

      // 获取token
      const token = uni.getStorageSync('accessToken');

      // 使用封装好的API方法
      joinStudyRoom({
          roomId: roomId,
        password: password,
        token: 'Bearer ' + token
      })
        .then(response => {
          uni.hideLoading();

            uni.showToast({
              title: '加入成功',
              icon: 'success'
            });

          // 加入成功后缓存房间ID
          uni.setStorageSync('lastJoinedRoom', roomId);

          // 直接进入自习室详情页面
          uni.navigateTo({
            url: '/pages/time/TimeStudyRoomDetail?roomId=' + roomId
          });

          // 刷新列表
            setTimeout(() => {
            this.loadRooms(true);
          }, 500);
        })
        .catch(error => {
          uni.hideLoading();
          console.error('加入自习室失败:', error);
        });
    },

    // 获取环境图标
    getEnvIcon(env) {
      switch (env) {
        case 'library':
          return 'icon-library';
        case 'cafe':
          return 'icon-cafe';
        case 'classroom':
          return 'icon-classroom';
        case 'nature':
          return 'icon-nature';
        case 'custom':
          return 'icon-custom';
        default:
          return 'icon-library';
      }
    },

    // 获取环境名称
    getEnvName(env) {
      switch (env) {
        case 'library':
          return '图书馆';
        case 'cafe':
          return '咖啡厅';
        case 'classroom':
          return '教室';
        case 'nature':
          return '自然';
        case 'custom':
          return '自定义';
        default:
          return '图书馆';
      }
    },

    // 获取加入按钮文本
    getJoinButtonText(room) {
      if (room.isSelf) {
        return '我的';
      } else if (room.isJoined) {
        return '已加入';
      } else {
        return '加入';
      }
    },

    // 处理自习室解散事件
    handleRoomDisbanded(roomId) {
      console.log('自习室已解散，刷新列表：', roomId);

      // 清理本地缓存
      try {
        // 如果是最后加入的自习室，清除缓存
        const lastJoinedRoom = uni.getStorageSync('lastJoinedRoom');
        if (lastJoinedRoom === roomId) {
          uni.removeStorageSync('lastJoinedRoom');
        }

        // 清除自习室缓存数据
        uni.removeStorageSync('cachedRoom_' + roomId);
      } catch (e) {
        console.error('清理自习室缓存失败:', e);
      }

      // 刷新自习室列表
      this.refreshRooms();
    }
  }
};
</script>

<style>
/* 全局容器样式 */
.study-room-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  position: relative;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  padding-top: 10px;
  background-color: #ffffff;
}

/* 区域样式 */
.section {
  margin-bottom: 20px;
  background-color: transparent;
}

.all-rooms-section {
  padding: 0 16px;
}

/* 搜索和刷新容器 */
.search-refresh-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
}

/* 搜索框样式 */
.search-box {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
  height: 40px;
}

.search-icon {
  font-size: 16px;
  color: #999999;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  font-size: 14px;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
}

.clear-icon {
  font-size: 16px;
  color: #999999;
  margin-left: 8px;
  cursor: pointer;
}

/* 刷新按钮 */
.refresh-btn {
  width: 40px;
  height: 40px;
  background-color: #5B7FFF;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.refresh-icon {
  font-size: 18px;
  color: #ffffff;
}

/* 区域内容 */
.section-content {
  background-color: transparent;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 14px;
  color: #999999;
  line-height: 1.5;
}

/* 自习室网格布局 */
.room-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding-bottom: 20px;
}

/* 自习室卡片样式 */
.room-card {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  min-height: 120px;
  transition: all 0.3s ease;
}

.room-card:active {
  transform: scale(0.98);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 卡片左侧人数区域 */
.room-card-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.user-icon {
  font-size: 14px;
  color: #5B7FFF;
  margin-right: 6px;
}

.capacity-text {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 卡片内容区域 */
.room-card-content {
  flex: 1;
  margin-bottom: 12px;
}

.room-name {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 标签区域 */
.room-tags {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 4px;
}

.room-tag {
  font-size: 10px;
  color: #5B7FFF;
  background-color: rgba(91, 127, 255, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-tags {
  font-size: 10px;
  color: #999999;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 8px;
}

/* 加入按钮 */
.join-btn {
  background-color: #5B7FFF;
  border-radius: 6px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.join-btn.joined {
  background-color: #f0f0f0;
}

.join-text {
  font-size: 12px;
  color: #ffffff;
  font-weight: 500;
}

.join-btn.joined .join-text {
  color: #666666;
}

/* 悬浮创建按钮 */
.floating-create-btn {
  position: fixed;
  right: 20px;
  bottom: 100px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.3);
  z-index: 100;
}

.create-icon {
  font-size: 24px;
  color: #ffffff;
  font-weight: bold;
}

/* 密码弹窗样式 */
.password-popup {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 24px;
  width: 280px;
  max-width: 90vw;
}

.popup-header {
  text-align: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8px;
  display: block;
}

.popup-subtitle {
  font-size: 14px;
  color: #666666;
  line-height: 1.4;
  display: block;
}

.popup-content {
  display: flex;
  flex-direction: column;
}

.password-input {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  color: #333333;
  margin-bottom: 20px;
  outline: none;
}

.password-input:focus {
  border-color: #5B7FFF;
  background-color: #ffffff;
}

.password-btns {
  display: flex;
  flex-direction: row;
  gap: 12px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666666;
}

.cancel-btn:active {
  background-color: #e0e0e0;
}

.confirm-btn {
  background-color: #5B7FFF;
  color: #ffffff;
}

.confirm-btn:active {
  background-color: #4A6FE7;
}
</style>

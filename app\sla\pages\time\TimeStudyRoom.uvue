<template>
  <view class="study-room-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="iconfont icon-arrow-left"></text>
        </view>
        <view class="nav-title">
          <text class="title-text">{{ roomInfo.name || '自习室' }}</text>
        </view>
        <view class="nav-right">
          <view class="exit-icon" @click="exitRoom" v-if="!isCreator">
            <text class="iconfont icon-exit"></text>
          </view>
          <view class="exit-icon" @click="disbandRoom" v-else>
            <text class="iconfont icon-delete"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域滚动容器 -->
    <scroll-view class="content-container" scroll-y>
      <!-- 自习室信息卡片 -->
      <view class="room-info-card">
        <view class="room-header">
          <view class="room-title">
            <text class="title">{{ roomInfo.name }}</text>
            <view class="tag-container">
              <text class="tag" v-for="(tag, index) in roomInfo.tags" :key="index">{{ tag }}</text>
            </view>
          </view>
          <view class="room-members-count">
            <text class="iconfont icon-user"></text>
            <text>{{ roomInfo.currentMembers || roomInfo.currentUsers || 0 }}/{{ roomInfo.capacity || 8 }}</text>
          </view>
        </view>
        <view class="room-desc">
          <text>{{ roomInfo.description || '暂无描述' }}</text>
        </view>
      </view>

      <!-- 状态切换器 -->
      <view class="status-switcher">
        <view
          v-for="(status, index) in statusOptions"
          :key="index"
          class="status-item"
          :class="{ active: currentStatus === status.value }"
          @click="changeStatus(status.value)"
        >
          <text class="iconfont" :class="status.icon"></text>
          <text class="status-text">{{ status.label }}</text>
        </view>
      </view>

      <!-- 环境选择器 -->
      <view class="environment-container">
        <view class="section-title">
          <text>学习环境</text>
        </view>
        <scroll-view class="environment-scroll" scroll-x show-scrollbar="false">
          <view
            v-for="(env, index) in environmentOptions"
            :key="index"
            class="environment-item"
            :class="{ active: currentEnvironment === env.value }"
            @click="changeEnvironment(env.value)"
          >
            <view class="env-icon">
              <text class="iconfont" :class="env.icon"></text>
            </view>
            <text class="env-text">{{ env.label }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 成员和聊天标签页 -->
      <view class="tabs-container">
        <view class="tabs-header">
          <view
            class="tab-item"
            :class="{ active: activeTab === 'members' }"
            @click="activeTab = 'members'"
          >
            <text>成员列表</text>
          </view>
          <view
            class="tab-item"
            :class="{ active: activeTab === 'chat' }"
            @click="activeTab = 'chat'"
          >
            <text>聊天</text>
          </view>
        </view>
        <view class="tab-content">
          <!-- 成员列表 -->
          <view class="members-container" v-show="activeTab === 'members'">
            <view
              v-for="(member, index) in members"
              :key="index"
              class="member-item"
            >
              <image class="member-avatar" :src="member.avatar" mode="aspectFill"></image>
              <view class="member-info">
                <view class="member-name-row">
                  <text class="member-name">{{ member.username }}</text>
                  <text class="member-creator" v-if="member.isCreator">创建者</text>
                </view>
                <view class="member-status">
                  <text class="status-dot" :class="getStatusClass(member.status)"></text>
                  <text class="status-text">{{ getStatusText(member.status) }}</text>
                </view>
              </view>
              <view class="member-focus-time">
                <text>{{ formatFocusTime(member.totalFocusTime) }}</text>
              </view>
            </view>
          </view>

          <!-- 聊天区域 -->
          <view class="chat-container" v-show="activeTab === 'chat'">
            <scroll-view class="chat-messages" scroll-y :scroll-into-view="'msg-' + messages.length">
              <view
                v-for="(message, index) in messages"
                :key="index"
                class="message-item"
                :class="{
                  'system-message': message.type === 'SYSTEM_JOIN' || message.type === 'SYSTEM_LEAVE' || message.type === 'SYSTEM_DISBAND',
                  'self-message': message.senderId === userId
                }"
                :id="'msg-' + index"
              >
                <!-- 系统消息 -->
                <view class="system-message-content" v-if="message.type === 'SYSTEM_JOIN' || message.type === 'SYSTEM_LEAVE' || message.type === 'SYSTEM_DISBAND'">
                  <text>{{ message.data.content }}</text>
                </view>

                <!-- 用户消息 -->
                <view class="user-message-content" v-else>
                  <view class="message-sender" v-if="message.senderId !== userId">
                    <image class="sender-avatar" :src="getSenderAvatar(message.senderId)" mode="aspectFill"></image>
                    <text class="sender-name">{{ getSenderName(message.senderId) }}</text>
                  </view>
                  <view class="message-content">
                    <text>{{ message.data.content }}</text>
                  </view>
                  <text class="message-time">{{ formatMessageTime(message.timestamp) }}</text>
                </view>
              </view>
            </scroll-view>

            <view class="chat-input-container">
              <input
                class="chat-input"
                v-model="messageContent"
                placeholder="输入消息..."
                @confirm="sendMessage"
              />
              <button class="send-btn" @click="sendMessage">发送</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <button class="exit-btn" v-if="!isCreator" @click="exitRoom">退出自习室</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20, // 状态栏高度，通过 uni.getSystemInfoSync 获取
      roomId: '', // 自习室ID
      roomInfo: {}, // 自习室信息
      members: [], // 成员列表
      messages: [], // 消息列表
      messageContent: '', // 消息输入内容
      activeTab: 'members', // 活动标签页: members/chat
      currentStatus: 'focusing', // 当前状态
      currentEnvironment: 'library', // 当前环境
      isCreator: false, // 是否是创建者
      userId: 0, // 当前用户ID
      webSocket: null, // WebSocket连接
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 5, // 最大重连尝试次数

      // 状态选项
      statusOptions: [
        { label: '专注中', value: 'focusing', icon: 'icon-focus' },
        { label: '有疑问', value: 'question', icon: 'icon-question' },
        { label: '休息中', value: 'rest', icon: 'icon-rest' },
        { label: '暂时离开', value: 'away', icon: 'icon-away' }
      ],

      // 环境选项
      environmentOptions: [
        { label: '图书馆', value: 'library', icon: 'icon-library' },
        { label: '咖啡厅', value: 'cafe', icon: 'icon-cafe' },
        { label: '教室', value: 'classroom', icon: 'icon-classroom' },
        { label: '自然', value: 'nature', icon: 'icon-nature' },
        { label: '自定义', value: 'custom', icon: 'icon-custom' }
      ]
    };
  },
  onLoad(options) {
    // ???????
    const sysInfo = uni.getSystemInfoSync();
    this.statusBarHeight = sysInfo.statusBarHeight;

    // ?????ID???ID
    this.roomId = options.roomId || '';
    this.getUserInfo();

    // ???????
    this.fetchRoomDetail();
  },
  onShow() {
    // ??WebSocket??
    this.connectWebSocket();
  },
  onHide() {
    // ??WebSocket??
    this.closeWebSocket();
  },
  onUnload() {
    // ??WebSocket??
    this.closeWebSocket();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 获取用户信息
    getUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo) {
          this.userId = userInfo.id;
        }
      } catch (e) {
        console.error('获取用户信息失败', e);
      }
    },

    // 获取自习室详情
    fetchRoomDetail() {
      uni.showLoading({ title: '加载中' });
      const token = uni.getStorageSync('token');

      uni.request({
        url: '/api/time/studyroom/detail/' + this.roomId,
        method: 'GET',
        header: {
          'Authorization': 'Bearer ' + token
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.roomInfo = res.data.data;
            this.members = res.data.data.members || [];
            this.isCreator = res.data.data.isSelf || false;
            this.currentEnvironment = res.data.data.environment || 'library';
          } else {
            uni.showToast({
              title: res.data.message || '获取自习室详情失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('获取自习室详情失败', err);
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    },

    // 切换状态
    changeStatus(status) {
      this.currentStatus = status;

      // 通过WebSocket发送状态变更消息
      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        const message = {
          type: 'STATUS_CHANGE',
          data: {
            status: status
          },
          timestamp: new Date().getTime(),
          senderId: this.userId
        };

        this.webSocket.send(JSON.stringify(message));
      }
    },

    // 切换环境
    changeEnvironment(environment) {
      // 只有创建者才能更改自习室环境
      if (!this.isCreator) {
        uni.showToast({
          title: '只有创建者可以更改环境',
          icon: 'none'
        });
        return;
      }

      this.currentEnvironment = environment;

      // 发送请求更新自习室环境
      const token = uni.getStorageSync('token');

      uni.request({
        url: '/api/time/studyroom/update',
        method: 'PUT',
        header: {
          'Authorization': 'Bearer ' + token,
          'Content-Type': 'application/json'
        },
        data: {
          roomId: this.roomId,
          environment: environment
        },
        success: (res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: '环境已更新',
              icon: 'success'
            });
          } else {
            uni.showToast({
              title: res.data.message || '更新环境失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('更新环境失败', err);
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
        }
      });
    },

    // 发送消息
    sendMessage() {
      if (!this.messageContent.trim()) {
        return;
      }

      // 通过WebSocket发送消息
      if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
        const message = {
          type: 'USER_MESSAGE',
          data: {
            content: this.messageContent.trim()
          },
          timestamp: new Date().getTime(),
          senderId: this.userId
        };

        this.webSocket.send(JSON.stringify(message));
        this.messageContent = '';
      } else {
        uni.showToast({
          title: '连接断开，请重新进入',
          icon: 'none'
        });
      }
    },

    // 连接WebSocket服务
    connectWebSocket() {
      const token = uni.getStorageSync('token');
      // 根据环境确定WebSocket的URL，开发环境使用localhost
      const serverHost = process.env.NODE_ENV === 'development'
        ? 'ws://127.0.0.1:8080'
        : 'wss://yourdomain.com'; // 生产环境应使用实际WebSocket地址

      const wsUrl = `${serverHost}/api/time/ws/studyroom/${this.roomId}?token=${token}`;

      // 关闭之前的连接
      this.closeWebSocket();

      // 创建新的WebSocket连接
      try {
        this.webSocket = new WebSocket(wsUrl);

        // 连接成功
        this.webSocket.onopen = () => {
          console.log('WebSocket连接成功');

          // 请求历史消息
          this.webSocket.send(JSON.stringify({
            type: 'GET_HISTORY',
            timestamp: new Date().getTime(),
            senderId: this.userId
          }));
        };

        // 接收消息
        this.webSocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('收到WebSocket消息:', message.type);

            switch (message.type) {
              case 'HISTORY_RESPONSE':
                // 历史消息处理
                if (message.data && message.data.messages) {
                  this.messages = message.data.messages;
                }
                break;
              case 'USER_MESSAGE':
                // 用户消息处理
                this.messages.push(message);
                // 滚动到最新消息
                this.$nextTick(() => {
                  const messagesElement = document.getElementById('msg-' + (this.messages.length - 1));
                  if (messagesElement) {
                    messagesElement.scrollIntoView();
                  }
                });
                break;
              case 'SYSTEM_JOIN':
              case 'SYSTEM_LEAVE':
                // 系统加入/离开消息
                this.messages.push(message);
                break;
              case 'SYSTEM_DISBAND':
                // 自习室解散消息
                this.messages.push(message);
                // 提示用户自习室已解散
                uni.showModal({
                  title: '提示',
                  content: '自习室已被创建者解散',
                  showCancel: false,
                  success: () => {
                    uni.navigateBack();
                  }
                });
                break;
              case 'MEMBER_LIST_UPDATE':
                // 成员列表更新
                if (message.data && message.data.members) {
                  this.members = message.data.members;
                }
                break;
              case 'ERROR':
                // 错误消息处理
                uni.showToast({
                  title: message.data.message || '发生错误',
                  icon: 'none'
                });
                break;
              default:
                console.log('未处理的消息类型', message);
            }
          } catch (e) {
            console.error('解析消息出错', e, event.data);
          }
        };

        // 连接关闭
        this.webSocket.onclose = (event) => {
          console.log('WebSocket连接关闭', event.code, event.reason);
          if (event.code !== 1000) {
            // 非正常关闭，尝试重连
            this.attemptReconnect();
          }
        };

        // 连接错误
        this.webSocket.onerror = (error) => {
          console.error('WebSocket连接错误', error);
          uni.showToast({
            title: '连接服务器失败',
            icon: 'none'
          });
        };
      } catch (e) {
        console.error('创建WebSocket连接失败', e);
        uni.showToast({
          title: '连接服务器失败',
          icon: 'none'
        });
      }
    },

    // 尝试重连
    attemptReconnect() {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        uni.showToast({
          title: '重连失败，请重新进入自习室',
          icon: 'none',
          duration: 3000
        });
        return;
      }

      this.reconnectAttempts++;
      console.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

      setTimeout(() => {
        this.connectWebSocket();
      }, 2000 * Math.min(this.reconnectAttempts, 10)); // 递增重连延迟，最多20秒
    },

    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.webSocket) {
        try {
          this.webSocket.close(1000, 'Normal Closure');
        } catch (e) {
          console.error('关闭WebSocket连接失败', e);
        }
        this.webSocket = null;
      }
      this.reconnectAttempts = 0;
    },

    // 退出自习室
    exitRoom() {
      uni.showModal({
        title: '提示',
        content: '确定要退出自习室吗？',
        success: (res) => {
          if (res.confirm) {
            const token = uni.getStorageSync('token');

            uni.request({
              url: '/api/time/studyroom/exit',
              method: 'POST',
              header: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
              },
              data: {
                roomId: this.roomId,
                focusTime: 0, // 这里可以传递专注时长
                recordFocusTime: true
              },
              success: (res) => {
                if (res.data.code === 200) {
                  uni.showToast({
                    title: '已退出自习室',
                    icon: 'success'
                  });

                  // 关闭WebSocket连接
                  this.closeWebSocket();

                  // 通知列表页面刷新
                  uni.$emit('refreshStudyRoomList');

                  // 设置需要刷新自习室列表
                  uni.setStorageSync('needRefreshStudyRoomList', true);

                  // 返回首页
                  setTimeout(() => {
                    uni.switchTab({
                      url: '/pages/time/index'
                    });
                  }, 1500);
                } else {
                  uni.showToast({
                    title: res.data.message || '退出自习室失败',
                    icon: 'none'
                  });
                }
              },
              fail: (err) => {
                console.error('退出自习室失败', err);
                uni.showToast({
                  title: '网络请求失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    // 解散自习室
    disbandRoom() {
      uni.showModal({
        title: '提示',
        content: '确定要解散自习室吗？所有成员将被移出',
        success: (res) => {
          if (res.confirm) {
            const token = uni.getStorageSync('token');

            uni.request({
              url: '/api/time/studyroom/disband',
              method: 'POST',
              header: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
              },
              data: {
                roomId: this.roomId
              },
              success: (res) => {
                if (res.data.code === 200) {
                  uni.showToast({
                    title: '自习室已解散',
                    icon: 'success'
                  });

                  // 关闭WebSocket连接
                  this.closeWebSocket();

                  // 通知列表页面刷新
                  uni.$emit('refreshStudyRoomList');

                  // 设置需要刷新自习室列表
                  uni.setStorageSync('needRefreshStudyRoomList', true);

                  // 返回
                  setTimeout(() => {
                    uni.switchTab({
                      url: '/pages/time/index'
                    });
                  }, 1500);
                } else {
                  uni.showToast({
                    title: res.data.message || '解散自习室失败',
                    icon: 'none'
                  });
                }
              },
              fail: (err) => {
                console.error('解散自习室失败', err);
                uni.showToast({
                  title: '网络请求失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    // 格式化专注时间
    formatFocusTime(minutes) {
      if (!minutes) return '0分钟';

      if (minutes < 60) {
        return minutes + '分钟';
      } else {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours + '小时' + (mins > 0 ? mins + '分钟' : '');
      }
    },

    // 格式化消息时间
    formatMessageTime(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return hours + ':' + minutes;
    },

    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 'focusing':
          return 'status-focusing';
        case 'question':
          return 'status-question';
        case 'rest':
          return 'status-rest';
        case 'away':
          return 'status-away';
        default:
          return '';
      }
    },

    // 获取状态文本描述
    getStatusText(status) {
      switch (status) {
        case 'focusing':
          return '专注中';
        case 'question':
          return '有疑问';
        case 'rest':
          return '休息中';
        case 'away':
          return '暂时离开';
        default:
          return '未知';
      }
    },

    // 获取发送者头像
    getSenderAvatar(senderId) {
      const member = this.members.find(m => m.userId === senderId);
      return member ? member.avatar : '/static/images/default-avatar.png';
    },

    // 获取发送者名称
    getSenderName(senderId) {
      const member = this.members.find(m => m.userId === senderId);
      return member ? member.username : '未知用户';
    }
  }
};
</script>

<style>
.study-room-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* ????????? */
.header {
  background-color: #5B7FFF;
  color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.status-bar {
  width: 100%;
}

.nav-bar {
  height: 44px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.nav-left {
  width: 40px;
  display: flex;
  align-items: center;
}

.nav-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 18px;
  font-weight: bold;
}

.nav-right {
  width: 40px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.exit-icon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-exit {
  font-size: 18px;
  color: #fff;
}

/* ????????*/
.content-container {
  flex: 1;
  padding: 15px;
}

.room-info-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.room-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.room-title {
  flex: 1;
  font-size: 18px;
  font-weight: bold;
}

.tag-container {
  display: inline;
}

.tag {
  background-color: #e0e0e0;
  border-radius: 4px;
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
  display: flex;
}

.room-members-count {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.room-desc {
  font-size: 14px;
}

/* ????? */
.status-switcher {
  display: flex;
  margin-bottom: 15px;
}

.status-item {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.status-item.active {
  border-bottom: 2px solid #5B7FFF;
}

.status-text {
  display: flex;
  margin-top: 5px;
}

/* ??????*/
.environment-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.environment-scroll {
  display: flex;
  overflow-x: auto;
  padding: 5px 0;
}

.environment-item {
  flex: 0 0 auto;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.environment-item.active {
  border-bottom: 2px solid #5B7FFF;
}

.env-icon {
  font-size: 24px;
}

.env-text {
  display: flex;
  margin-top: 5px;
}

/* ??????????*/
.tabs-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.tabs-header {
  display: flex;
  margin-bottom: 15px;
}

.tab-item {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  border-bottom: 2px solid #5B7FFF;
}

.tab-content {
  flex: 1;
}

.members-container {
  margin-bottom: 15px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 999px;
  margin-right: 10px;
}

.member-info {
  flex: 1;
}

.member-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.member-name {
  font-size: 14px;
  font-weight: bold;
}

.member-creator {
  background-color: #e0e0e0;
  border-radius: 4px;
  padding: 2px 8px;
  margin-left: 8px;
  font-size: 12px;
}

.member-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 999px;
  margin-right: 5px;
}

.status-text {
  font-size: 12px;
}

.member-focus-time {
  font-size: 12px;
}

.chat-container {
  flex: 1;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
}

.message-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.system-message-content {
  font-size: 14px;
}

.user-message-content {
  display: flex;
  align-items: center;
}

.message-sender {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.sender-avatar {
  width: 40px;
  height: 40px;
  border-radius: 999px;
  margin-right: 5px;
}

.sender-name {
  font-size: 14px;
}

.message-content {
  flex: 1;
}

.message-time {
  font-size: 12px;
}

.chat-input-container {
  display: flex;
  align-items: center;
  padding: 10px;
  border-top: 1px solid #e0e0e0;
}

.chat-input {
  flex: 1;
  padding: 10px;
}

.send-btn {
  padding: 10px 20px;
  background-color: #5B7FFF;
  color: #fff;
  border: none;
  border-radius: 4px;
  margin-left: 10px;
}

/* ?????? */
.bottom-actions {
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
}

.exit-btn {
  width: 100%;
  padding: 10px;
  background-color: #5B7FFF;
  color: #fff;
  border: none;
  border-radius: 4px;
}
</style>

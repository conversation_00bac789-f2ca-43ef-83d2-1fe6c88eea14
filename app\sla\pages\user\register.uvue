<template>
  <view class="register-container">
    <!-- 导航栏区域 -->
    <sla-navbar title="注册" @back="navigateBack" />

    <sla-form class="register-form" :model="formData" :rules="rules" ref="registerForm">
      <sla-input
        v-model="formData.username"
        label="用户名"
        placeholder="请输入用户名"
        :required="true"
        :clearable="true"
        :error="$refs.registerForm?.getFieldError('username')"
      />

      <sla-input
        v-model="formData.phone"
        label="手机号"
        placeholder="请输入手机号"
        type="number"
        :maxlength="11"
        :required="true"
        :clearable="true"
        :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('phone') : ''"
      />

      <view class="verification-code">
        <sla-input
          v-model="formData.code"
          label="验证码"
          placeholder="请输入验证码"
          type="number"
          :maxlength="6"
          :required="true"
          :clearable="true"
          :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('code') : ''"
        >
          <template v-slot:suffix>
            <sla-countdown
              ref="countdown"
              @click="sendVerificationCode"
              :disabled="!isPhoneValid"
            />
          </template>
        </sla-input>
      </view>

      <sla-input
        v-model="formData.password"
        label="密码"
        placeholder="请输入密码"
        type="password"
        :required="true"
        :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('password') : ''"
      />

      <sla-input
        v-model="formData.confirmPassword"
        label="确认密码"
        placeholder="请再次输入密码"
        type="password"
        :required="true"
        :error="$refs.registerForm?.getFieldError ? $refs.registerForm.getFieldError('confirmPassword') : ''"
      />

      <view class="register-agreement">
        <checkbox :checked="agreement" @click="toggleAgreement" color="#80B8F5" />
        <text class="register-agreement__text">我已阅读并同意</text>
        <text class="register-agreement__link" @click="showAgreement">用户协议</text>
        <text class="register-agreement__text">和</text>
        <text class="register-agreement__link" @click="showPrivacy">隐私政策</text>
      </view>

      <sla-button
        text="注册"
        type="primary"
        size="large"
        :loading="loading"
        :disabled="!agreement"
        @click="handleRegister"
        class="register-button"
      />

      <view class="register-login">
        <text class="register-login__text">已有账号？</text>
        <text class="register-login__link" @click="navigateToLogin">立即登录</text>
      </view>
    </sla-form>
  </view>
</template>

<script>
import SlaButton from '../../components/user/SlaButton.uvue';
import SlaInput from '../../components/user/SlaInput.uvue';
import SlaForm from '../../components/user/SlaForm.uvue';
import SlaCountdown from '../../components/user/SlaCountdown.uvue';
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import { register, sendSms } from '../../utils/api/user.js';

export default {
  components: {
    SlaButton,
    SlaInput,
    SlaForm,
    SlaCountdown,
    SlaNavbar
  },
  data() {
    return {
      // 表单数据
      formData: {
        username: '',
        phone: '',
        code: '',
        password: '',
        confirmPassword: ''
      },
      // 表单验证规则
      rules: {
        username: [
          { required: true, message: '请输入用户名' },
          { min: 2, max: 64, message: '用户名长度为2-64个字符' }
        ],
        phone: [
          { required: true, message: '请输入手机号' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
        ],
        code: [
          { required: true, message: '请输入验证码' },
          { pattern: /^\d{6}$/, message: '验证码为6位数字' }
        ],
        password: [
          { required: true, message: '请输入密码' },
          { min: 6, max: 20, message: '密码长度为6-20个字符' },
          {
            pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,20}$/,
            message: '密码必须包含字母和数字'
          }
        ],
        confirmPassword: [
          {
            required: true,
            message: '请再次输入密码',
            validator: (rule, value, callback) => {
              if (value !== this.formData.password) {
                callback('两次输入的密码不一致');
              } else {
                callback();
              }
            }
          }
        ]
      },
      // 用户协议同意状态
      agreement: false,
      // 注册加载状态
      loading: false
    };
  },
  computed: {
    // 手机号格式验证
    isPhoneValid() {
      return this.formData.phone && /^1[3-9]\d{9}$/.test(this.formData.phone);
    }
  },
  methods: {
    // 发送验证码
    sendVerificationCode() {
      // 验证手机号格式
      if (!this.isPhoneValid) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return false;
      }

      // 显示加载
      uni.showLoading({
        title: '发送中...'
      });

      // 发送验证码
      sendSms(this.formData.phone, 0).then(() => {
        uni.hideLoading();
        uni.showToast({
          title: '验证码发送成功',
          icon: 'success'
        });
        // 启动倒计时
        const countdown = this.$refs.countdown as any;
        if (countdown && typeof countdown.start === 'function') {
          countdown.start();
        }
      }).catch(err => {
        uni.hideLoading();
        uni.showToast({
          title: err.message || '验证码发送失败',
          icon: 'none'
        });
      });
    },

    // 处理注册
    handleRegister() {
      if (!this.agreement) {
        uni.showToast({
          title: '请先同意用户协议和隐私政策',
          icon: 'none'
        });
        return;
      }

      // 表单验证
      const registerForm = this.$refs.registerForm as any;
      if (registerForm && typeof registerForm.validate === 'function') {
        registerForm.validate().then(({ valid, errors }) => {
          if (!valid) {
            const firstError = Object.values(errors)[0] as string;
            uni.showToast({
              title: firstError,
              icon: 'none'
            });
            return;
          }

        this.loading = true;

        // 构建注册参数
        const registerParams = {
          username: this.formData.username,
          phone: this.formData.phone,
          code: this.formData.code,
          password: this.formData.password
        };

        // 调用注册接口
        register(registerParams).then(res => {
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          });

          // 保存token和用户信息
          if (res.data) {
            uni.setStorageSync('accessToken', res.data.accessToken);
            uni.setStorageSync('refreshToken', res.data.refreshToken);
            uni.setStorageSync('userInfo', res.data);
          }

          // 跳转到首页
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        }).catch(err => {
          uni.showToast({
            title: err.message || '注册失败',
            icon: 'none'
          });
        }).finally(() => {
          this.loading = false;
        });
      });
      } else {
        uni.showToast({
          title: '表单验证失败',
          icon: 'none'
        });
      }
    },

    // 切换协议同意状态
    toggleAgreement() {
      this.agreement = !this.agreement;
    },

    // 显示用户协议
    showAgreement() {
      uni.showModal({
        title: '用户协议',
        content: '用户协议内容...',
        showCancel: false
      });
    },

    // 显示隐私政策
    showPrivacy() {
      uni.showModal({
        title: '隐私政策',
        content: '隐私政策内容...',
        showCancel: false
      });
    },

    // 返回上一页
    navigateBack() {
      // 返回到登录页面，避免返回到splash页面
      uni.navigateTo({
        url: '/pages/user/login'
      });
    },

    // 跳转到登录页面
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/user/login'
      });
    }
  }
}
</script>

<style>
.register-container {
  min-height: 800px;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* 蓝色渐变背景 */
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 添加背景装饰效果 */
.register-container::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.register-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 30px;
  padding: 0 10px;
}

.register-back {
  padding: 8px;
}

.register-back__icon {
  font-size: 24px;
  color: #333333;
}

.register-form {
  background-color: rgba(255, 255, 255, 0.85); /* 半透明白色背景 */
  backdrop-filter: blur(10px); /* 毛玻璃效果 */
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.6); /* 添加边框 */
}

.verification-code {
  position: relative;
}

.register-agreement {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  margin: 16px 0;
}

.register-agreement__text {
  font-size: 14px;
  color: #666666;
  margin-left: 4px;
}

.register-agreement__link {
  font-size: 14px;
  color: #8A2BE2; /* 紫色链接 */
  cursor: pointer;
}

.register-button {
  width: 100%;
  margin-top: 24px;
  background: linear-gradient(135deg, #8A2BE2, #9370DB); /* 紫色渐变 */
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(138, 43, 226, 0.3);
}

.register-login {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 24px;
  position: relative;
  z-index: 1;
}

.register-login__text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.register-login__link {
  font-size: 14px;
  color: #FFFFFF;
  margin-left: 4px;
  font-weight: bold;
  text-decoration: underline;
  cursor: pointer;
}
</style>


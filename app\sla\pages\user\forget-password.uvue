<template>
  <view class="forget-password-container">
    <!-- 导航栏 -->
    <sla-navbar title="忘记密码" @back="navigateBack" />

    <view class="forget-password-form">
      <!-- 手机号输入 -->
      <view class="input-group">
        <text class="input-label">手机号</text>
        <input
          class="sla-input"
          v-model="formData.phone"
          placeholder="请输入手机号"
          type="text"
          maxlength="11"
          @input="onPhoneInput"
        />
        <text class="error-text" v-if="phoneError">{{ phoneError }}</text>
      </view>

      <!-- 验证码输入 -->
      <view class="input-group verification-code">
        <text class="input-label">验证码</text>
        <view class="verification-code-container">
          <input
            class="sla-input verification-code-input"
            v-model="formData.code"
            placeholder="请输入验证码"
            type="number"
            maxlength="6"
            @input="onCodeInput"
          />
          <view
            class="verification-code-button"
            :class="{
              'verification-code-button--active': canSendCode,
              'verification-code-button--disabled': !canSendCode
            }"
            @click="sendVerificationCode"
          >
            <text class="verification-code-text" :style="canSendCode ? 'color: #FFFFFF;' : ''">
              {{ countdownText }}
            </text>
          </view>
        </view>
        <text class="error-text" v-if="codeError">{{ codeError }}</text>
      </view>

      <!-- 新密码输入 -->
      <view class="input-group">
        <text class="input-label">新密码</text>
        <input
          class="sla-input"
          v-model="formData.newPassword"
          placeholder="请输入新密码"
          type="password"
          @input="onPasswordInput"
        />
        <text class="error-text" v-if="passwordError">{{ passwordError }}</text>
      </view>

      <!-- 确认密码输入 -->
      <view class="input-group">
        <text class="input-label">确认密码</text>
        <input
          class="sla-input"
          v-model="formData.confirmPassword"
          placeholder="请再次输入新密码"
          type="password"
          @input="onConfirmPasswordInput"
        />
        <text class="error-text" v-if="confirmPasswordError">{{ confirmPasswordError }}</text>
      </view>

      <!-- 重置密码按钮 -->
      <view class="forget-password-button-container">
        <button
          class="forget-password-button"
          :loading="loading"
          @click="handleResetPassword"
        >重置密码</button>
      </view>

      <view class="forget-password-login">
        <text class="forget-password-login__text">想起密码了？</text>
        <text class="forget-password-login__link" @click="navigateToLogin">立即登录</text>
      </view>
    </view>
  </view>
</template>

<script>
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import { forgetPassword, sendSms } from '../../utils/api/user.js';

export default {
  components: {
    SlaNavbar
  },
  data() {
    return {
      // 表单数据
      formData: {
        phone: '',
        code: '',
        newPassword: '',
        confirmPassword: ''
      },
      // 错误信息
      phoneError: '',
      codeError: '',
      passwordError: '',
      confirmPasswordError: '',
      // 加载状态
      loading: false,
      // 验证码发送状态
      isSending: false,
      // 倒计时相关
      countdown: 60,
      timer: null,
      isCountingDown: false
    };
  },
  computed: {
    // 是否可以发送验证码
    canSendCode() {
      // 验证手机号格式
      const phonePattern = /^1[3-9]\d{9}$/;
      const isValidPhone = this.formData.phone && phonePattern.test(this.formData.phone);
      console.log('canSendCode计算属性, 手机号:', this.formData.phone, '是否有效:', isValidPhone);
      return isValidPhone && !this.isSending && !this.isCountingDown;
    },
    // 倒计时文本
    countdownText() {
      if (this.isCountingDown) {
        return `${this.countdown}秒后重发`;
      } else if (this.isSending) {
        return '发送中...';
      } else {
        return '获取验证码';
      }
    }
  },
  // 页面加载
  onLoad() {
    console.log('忘记密码页面加载');
    // 重置状态
    this.isSending = false;
    this.isCountingDown = false;
    this.countdown = 60;

    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  // 页面卸载
  onUnload() {
    console.log('忘记密码页面卸载');
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: {
    // 手机号输入处理
    onPhoneInput(event) {
      // 获取输入值，兼容不同平台
      const value = event.detail?.value || event.target?.value || '';
      console.log('手机号输入, 值:', value);
      this.formData.phone = value;

      // 验证手机号
      const phonePattern = /^1[3-9]\d{9}$/;
      if (!value) {
        this.phoneError = '请输入手机号';
      } else if (!phonePattern.test(value)) {
        this.phoneError = '手机号格式不正确';
      } else {
        this.phoneError = '';
      }
    },

    // 验证码输入处理
    onCodeInput(event) {
      const value = event.detail?.value || event.target?.value || '';
      this.formData.code = value;

      // 验证验证码
      if (!value) {
        this.codeError = '请输入验证码';
      } else if (!/^\d{6}$/.test(value)) {
        this.codeError = '验证码为6位数字';
      } else {
        this.codeError = '';
      }
    },

    // 新密码输入处理
    onPasswordInput(event) {
      const value = event.detail?.value || event.target?.value || '';
      this.formData.newPassword = value;

      // 验证密码
      if (!value) {
        this.passwordError = '请输入新密码';
      } else if (value.length < 6 || value.length > 20) {
        this.passwordError = '密码长度为6-20位字符';
      } else if (!/^(?=.*[A-Za-z])(?=.*\d)[\w\W]{6,20}$/.test(value)) {
        this.passwordError = '密码必须包含至少一个字母和一个数字';
      } else {
        this.passwordError = '';
      }

      // 如果确认密码已输入，重新验证
      if (this.formData.confirmPassword) {
        if (this.formData.confirmPassword !== value) {
          this.confirmPasswordError = '两次输入的密码不一致';
        } else {
          this.confirmPasswordError = '';
        }
      }
    },

    // 确认密码输入处理
    onConfirmPasswordInput(event) {
      const value = event.detail?.value || event.target?.value || '';
      this.formData.confirmPassword = value;

      // 验证确认密码
      if (!value) {
        this.confirmPasswordError = '请确认新密码';
      } else if (value !== this.formData.newPassword) {
        this.confirmPasswordError = '两次输入的密码不一致';
      } else {
        this.confirmPasswordError = '';
      }
    },

    // 发送验证码
    sendVerificationCode() {
      console.log('发送验证码, 手机号:', this.formData.phone);

      // 验证手机号格式
      const phonePattern = /^1[3-9]\d{9}$/;
      const isValidPhone = this.formData.phone && phonePattern.test(this.formData.phone);

      if (!isValidPhone) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 防止重复发送或倒计时中
      if (this.isSending || this.isCountingDown) {
        console.log('正在发送中或倒计时中');
        return;
      }

      // 设置发送状态
      this.isSending = true;

      // 显示加载
      uni.showLoading({
        title: '发送中...'
      });

      // 使用AuthCodeType.FORGOT_PASSWORD，值为2
      const forgotPasswordType = 2;
      console.log('调用发送验证码接口:', this.formData.phone, '类型:', forgotPasswordType);

      // 调用接口
      sendSms(this.formData.phone, forgotPasswordType)
        .then((res) => {
          console.log('验证码发送成功:', res);
          uni.hideLoading();
          uni.showToast({
            title: '验证码发送成功',
            icon: 'success',
            duration: 2000
          });

          // 开始倒计时
          this.startCountdown();
        })
        .catch(err => {
          console.error('验证码发送失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: err.message || '验证码发送失败',
            icon: 'none',
            duration: 2000
          });
        })
        .finally(() => {
          this.isSending = false;
        });
    },

    // 开始倒计时
    startCountdown() {
      this.isCountingDown = true;
      this.countdown = 60;

      // 清除之前的定时器
      if (this.timer) {
        clearInterval(this.timer);
      }

      // 启动新的定时器
      this.timer = setInterval(() => {
        if (this.countdown > 1) {
          this.countdown--;
        } else {
          // 倒计时结束
          this.stopCountdown();
        }
      }, 1000);
    },

    // 停止倒计时
    stopCountdown() {
      this.isCountingDown = false;
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 表单验证
    validateForm() {
      // 验证手机号
      const phonePattern = /^1[3-9]\d{9}$/;
      if (!this.formData.phone) {
        this.phoneError = '请输入手机号';
        return false;
      } else if (!phonePattern.test(this.formData.phone)) {
        this.phoneError = '手机号格式不正确';
        return false;
      } else {
        this.phoneError = '';
      }

      // 验证验证码
      if (!this.formData.code) {
        this.codeError = '请输入验证码';
        return false;
      } else if (!/^\d{6}$/.test(this.formData.code)) {
        this.codeError = '验证码为6位数字';
        return false;
      } else {
        this.codeError = '';
      }

      // 验证新密码
      if (!this.formData.newPassword) {
        this.passwordError = '请输入新密码';
        return false;
      } else if (this.formData.newPassword.length < 6 || this.formData.newPassword.length > 20) {
        this.passwordError = '密码长度为6-20位字符';
        return false;
      } else if (!/^(?=.*[A-Za-z])(?=.*\d)[\w\W]{6,20}$/.test(this.formData.newPassword)) {
        this.passwordError = '密码必须包含至少一个字母和一个数字';
        return false;
      } else {
        this.passwordError = '';
      }

      // 验证确认密码
      if (!this.formData.confirmPassword) {
        this.confirmPasswordError = '请确认新密码';
        return false;
      } else if (this.formData.confirmPassword !== this.formData.newPassword) {
        this.confirmPasswordError = '两次输入的密码不一致';
        return false;
      } else {
        this.confirmPasswordError = '';
      }

      return true;
    },

    // 重置密码
    handleResetPassword() {
      console.log('重置密码处理, 表单数据:', JSON.stringify(this.formData));

      // 表单验证
      if (!this.validateForm()) {
        console.log('表单验证失败');
        return;
      }

      this.loading = true;

      // 构建请求参数，符合ForgetPasswordDTO结构
      const resetParams = {
        phone: this.formData.phone,
        code: this.formData.code,
        newPassword: this.formData.newPassword
      };

      console.log('重置密码参数:', resetParams);

      // 调用重置密码接口
      forgetPassword(resetParams)
        .then((res) => {
          console.log('密码重置成功:', res);
          uni.showToast({
            title: '密码重置成功',
            icon: 'success',
            duration: 2000
          });

          // 跳转到登录页
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/user/login'
            });
          }, 1500);
        })
        .catch(err => {
          console.error('密码重置失败:', err);
          uni.showToast({
            title: err.message || '重置失败',
            icon: 'none',
            duration: 2000
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 返回上一页
    navigateBack() {
      // 返回到登录页面而不是splash页
      uni.navigateTo({
        url: '/pages/user/login'
      });
    },

    // 跳转到登录页
    navigateToLogin() {
      uni.navigateTo({
        url: '/pages/user/login'
      });
    }
  }
}
</script>

<style>
.forget-password-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4361ee 0%, #3a86ff 100%); /* 渐变背景 */
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 装饰元素 */
.forget-password-container::before {
  content: "";
  position: absolute;
  top: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 0;
}

.forget-password-container::after {
  content: "";
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 0;
}

.forget-password-form {
  background-color: rgba(255, 255, 255, 0.85); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 毛玻璃效果 */
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.6); /* 边框透明度 */
  margin-top: 20px;
}

/* 输入框组 */
.input-group {
  margin-bottom: 20px;
  position: relative;
}

.input-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
}

.sla-input {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  border: 1px solid rgba(138, 43, 226, 0.3);
  padding: 0 12px;
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.6);
  color: #333;
}

.error-text {
  font-size: 12px;
  color: #f44336;
  margin-top: 4px;
  display: block;
}

/* 验证码区域 */
.verification-code-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.verification-code-input {
  flex: 1;
  margin-right: 10px;
}

.verification-code-button {
  height: 44px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: rgba(240, 244, 255, 0.7); /* 默认背景 */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(138, 43, 226, 0.3); /* 边框颜色 */
  backdrop-filter: blur(5px); /* 模糊效果 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); /* 阴影效果 */
  min-width: 120px;
}

.verification-code-button--active {
  background-color: #8A2BE2; /* 激活背景 */
  box-shadow: 0 4px 10px rgba(138, 43, 226, 0.2);
  border: none;
  /* 可点击状态 */
  pointer-events: auto;
}

.verification-code-button--active:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(138, 43, 226, 0.2);
}

.verification-code-button--disabled {
  opacity: 0.6; /* 禁用状态透明度 */
  background-color: rgba(240, 244, 255, 0.8);
  border: 1px solid rgba(138, 43, 226, 0.15); /* 禁用状态边框 */
}

.verification-code-text {
  font-size: 13px;
  color: rgba(138, 43, 226, 0.7); /* 默认文字颜色 */
  white-space: nowrap;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  font-weight: 500; /* 字体粗细 */
}

.verification-code-button--active .verification-code-text {
  color: #FFFFFF;
  font-weight: 500;
}

/* 重置密码按钮 */
.forget-password-button-container {
  margin-top: 24px;
  margin-bottom: 16px;
}

.forget-password-button {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background: linear-gradient(135deg, #8A2BE2, #9370DB); /* 渐变背景 */
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(138, 43, 226, 0.3);
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

.forget-password-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(138, 43, 226, 0.2);
}

/* 登录链接 */
.forget-password-login {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 16px;
  position: relative;
  z-index: 1;
}

.forget-password-login__text {
  font-size: 14px;
  color: #333;
}

.forget-password-login__link {
  font-size: 14px;
  color: #8A2BE2;
  margin-left: 4px;
  font-weight: 600;
  text-decoration: underline;
}
</style>

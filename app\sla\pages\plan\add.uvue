<template>
  <view class="add-plan-container">
    <!-- ??????? -->
    <sla-navbar title="????" @back="goBack"></sla-navbar>

    <scroll-view class="form-container" scroll-y>
      <view class="form-item">
        <text class="form-label required">????</text>
        <input class="form-input" v-model="planForm.title" placeholder="???????" />
      </view>

      <view class="form-item">
        <text class="form-label required">????</text>
        <view class="plan-type-selector">
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': planForm.type === 'daily'}"
            @click="changePlanType('daily')"
          >
            <text class="plan-type-item__text">???</text>
          </view>
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': planForm.type === 'weekly'}"
            @click="changePlanType('weekly')"
          >
            <text class="plan-type-item__text">???</text>
          </view>
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': planForm.type === 'monthly'}"
            @click="changePlanType('monthly')"
          >
            <text class="plan-type-item__text">???</text>
          </view>
          <view
            class="plan-type-item"
            :class="{'plan-type-item--active': planForm.type === 'custom'}"
            @click="changePlanType('custom')"
          >
            <text class="plan-type-item__text">???</text>
          </view>
        </view>
        <text class="plan-type-hint">
          {{
            planForm.type === 'daily' ? '?????????' :
            planForm.type === 'weekly' ? '?????????' :
            planForm.type === 'monthly' ? '??????????' :
            '?????????????'
          }}
        </text>
      </view>

      <view class="form-item">
        <text class="form-label required">????</text>
        <picker v-if="planForm.type === 'daily'" mode="date" :value="planForm.date" @change="onPlanDateChange">
          <view class="picker-item">{{formatDate(planForm.date) || '?????'}}</view>
        </picker>
        <picker v-else-if="planForm.type === 'weekly'" mode="date" :value="planForm.date" @change="onPlanDateChange" :start="getWeekStart()" :end="getWeekEnd()">
          <view class="picker-item">{{formatWeekDate(planForm.date) || '????'}}</view>
        </picker>
        <picker v-else-if="planForm.type === 'monthly'" mode="date" fields="month" :value="planForm.date" @change="onPlanDateChange">
          <view class="picker-item">{{formatMonthDate(planForm.date) || '?????'}}</view>
        </picker>
        <picker v-else-if="planForm.type === 'custom'" mode="date" :value="planForm.date" @change="onPlanDateChange">
          <view class="picker-item">{{formatDate(planForm.date) || '?????'}}</view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">????</text>
        <view class="end-date-display">
          <text class="end-date-text">{{getEndDateDisplay()}}</text>
        </view>
      </view>

      <view class="form-item" v-if="planForm.type === 'custom'">
        <text class="form-label required">????</text>
        <view class="custom-days-selector">
          <slider min="1" max="120" :value="planForm.customDays" show-value @change="onCustomDaysChange" block-size="28" activeColor="#80B8F5" backgroundColor="#E0E0E0" block-color="#FFFFFF" style="width: 100%; height: 50px;"></slider>
          <text class="custom-days-value">{{planForm.customDays}}?</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">???</text>
        <view class="priority-selector">
          <view
            class="priority-item priority-item--low"
            :class="{'priority-item--active': planForm.priority === 'low'}"
            @click="planForm.priority = 'low'"
          >
            <text class="priority-item__text">?</text>
          </view>
          <view
            class="priority-item priority-item--medium"
            :class="{'priority-item--active': planForm.priority === 'medium'}"
            @click="planForm.priority = 'medium'"
          >
            <text class="priority-item__text">?</text>
          </view>
          <view
            class="priority-item priority-item--high"
            :class="{'priority-item--active': planForm.priority === 'high'}"
            @click="planForm.priority = 'high'"
          >
            <text class="priority-item__text">?</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">??</text>
        <textarea class="form-textarea" v-model="planForm.description" placeholder="??????????"></textarea>
      </view>

      <!-- ????? -->
      <view class="form-item">
        <view class="section-header">
          <text class="form-label">???</text>
          <view class="add-subtask-btn" @click="showAddSubTaskForm = true" v-if="!showAddSubTaskForm">
            <text class="add-subtask-icon">+</text>
            <text class="add-subtask-text">?????</text>
          </view>
        </view>

        <!-- ??????? -->
        <view class="subtask-form" v-if="showAddSubTaskForm">
          <view class="form-item subtask-form-item">
            <text class="form-label required">?????</text>
            <input class="form-input" v-model="newSubTask.title" placeholder="????????" />
          </view>

          <view class="form-item subtask-form-item">
            <text class="form-label">???</text>
            <view class="time-range-picker">
              <picker mode="time" :value="newSubTask.startTime" @change="onSubTaskStartTimeChange">
                <view class="picker-item">{{newSubTask.startTime || '????'}}</view>
              </picker>
              <text class="picker-separator">?</text>
              <picker mode="time" :value="newSubTask.endTime" @change="onSubTaskEndTimeChange">
                <view class="picker-item">{{newSubTask.endTime || '????'}}</view>
              </picker>
            </view>
          </view>

          <view class="form-item subtask-form-item">
            <text class="form-label">??</text>
            <textarea class="form-textarea" v-model="newSubTask.description" placeholder="?????????????"></textarea>
          </view>

          <view class="subtask-form-actions">
            <view class="btn-cancel" hover-class="btn-hover" @click="cancelAddSubTask">??</view>
            <view class="btn-primary" hover-class="btn-hover" @click="addSubTask">??</view>
          </view>
        </view>

        <!-- ????? -->
        <view class="subtask-list" v-if="planForm.subTasks && planForm.subTasks.length > 0">
          <view class="subtask-list-header">
            <text class="subtask-list-title">?? {{planForm.subTasks.length}} ????</text>
            <text class="subtask-list-subtitle">???????</text>
          </view>
          <view class="subtask-item" v-for="(subtask, index) in sortedSubTasks" :key="subtask.id">
            <view class="subtask-checkbox" :class="{'subtask-checkbox--checked': subtask.completed}"></view>
            <view class="subtask-content">
              <text class="subtask-title" :class="{'subtask-title--completed': subtask.completed}">{{subtask.title}}</text>
              <text class="subtask-time" v-if="subtask.startTime && subtask.endTime">{{subtask.startTime}} - {{subtask.endTime}}</text>
              <text class="subtask-description" v-if="subtask.description">{{subtask.description}}</text>
            </view>
            <view class="subtask-actions">
              <view class="subtask-action subtask-action--delete" @click="deleteSubTask(getOriginalIndex(subtask.id))">
                <text class="subtask-action__icon">??</text>
              </view>
            </view>
          </view>
        </view>

        <view class="empty-subtasks" v-else-if="!showAddSubTaskForm">
          <text class="empty-subtasks__text">??????????????</text>
        </view>
      </view>
    </scroll-view>

    <view class="footer">
      <view class="btn-cancel" hover-class="btn-hover" @click="goBack">??</view>
      <view class="btn-primary" hover-class="btn-hover" @click="savePlan">??</view>
    </view>
  </view>
</template>

<script>
import SlaNavbar from '../../components/user/SlaNavbar.uvue';
import planApi from '../../utils/api/plan.js';

// ?????????
interface SubTask {
  id: number;
  title: string;
  startTime: string;
  endTime: string;
  description: string;
  completed: boolean;
}

export default {
  components: {
    SlaNavbar
  },
  computed: {
    // ?????????????
    sortedSubTasks() {
      if (!this.planForm.subTasks || this.planForm.subTasks.length === 0) {
        return [];
      }

      // ????????????????
      return [...this.planForm.subTasks].sort((a, b) => {
        // ?????????????
        const aMinutes = this.timeToMinutes(a.startTime);
        const bMinutes = this.timeToMinutes(b.startTime);
        return aMinutes - bMinutes;
      });
    }
  },
  data() {
    return {
      planForm: {
        title: '',
        type: 'daily',
        date: new Date().toISOString().split('T')[0],
        priority: 'medium',
        description: '',
        completed: false,
        customDays: 1, // ??1?????????????
        subTasks: [] as SubTask[] // ?????????
      },
      showAddSubTaskForm: false, // ?????????
      newSubTask: {
        title: '',
        startTime: '09:00',
        endTime: '10:00',
        description: '',
        completed: false
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    // ??????
    changePlanType(type) {
      // ???????????
      if (this.planForm.type === type) return;

      this.planForm.type = type;

      // ???????????????
      if (type === 'daily') {
        // ???????1?
        this.planForm.customDays = 1;
      } else if (type === 'weekly') {
        // ???????7?
        this.planForm.customDays = 7;
        this.adjustToWeekStart();
      } else if (type === 'monthly') {
        // ???????30?
        this.planForm.customDays = 30;
        this.adjustToMonthStart();
      } else if (type === 'custom') {
        // ?????????7?
        this.planForm.customDays = 7;
      }

      // ???????
      if (this.planForm.subTasks.length > 0) {
        uni.showModal({
          title: '??????',
          content: '????????????????????????',
          success: (res) => {
            if (res.confirm) {
              this.planForm.subTasks = [];
            } else {
              // ??????????????
              this.planForm.type = type === 'daily' ? 'monthly' : (type === 'weekly' ? 'daily' : 'weekly');
            }
          }
        });
      }
    },

    onPlanDateChange(e) {
      this.planForm.date = e.detail.value;

      // ??????????????
      if (this.planForm.type === 'weekly') {
        this.adjustToWeekStart();
      }
      // ??????????????
      else if (this.planForm.type === 'monthly') {
        this.adjustToMonthStart();
      }
    },

    // ???????????
    adjustToWeekStart() {
      const date = new Date(this.planForm.date);
      const day = date.getDay(); // 0????1-6???????      const diff = day === 0 ? -6 : 1 - day; // ?????????
      date.setDate(date.getDate() + diff);
      this.planForm.date = date.toISOString().split('T')[0];
    },

    // ????????????
    adjustToMonthStart() {
      const date = new Date(this.planForm.date);
      date.setDate(1);
      this.planForm.date = date.toISOString().split('T')[0];
    },

    // ??????????????
    getWeekStart() {
      const now = new Date();
      const day = now.getDay();
      const diff = day === 0 ? -6 : 1 - day; // ?????????
      now.setDate(now.getDate() + diff);
      return now.toISOString().split('T')[0];
    },

    // ??????????????
    getWeekEnd() {
      const now = new Date();
      now.setFullYear(now.getFullYear() + 1);
      return now.toISOString().split('T')[0];
    },

    // ????????????
    formatDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');

      return `${year}??{month}??{day}?`;
    },

    // ????????
    formatWeekDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;

      // ????
      const firstDayOfYear = new Date(year, 0, 1);
      const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
      const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);

      return `${year}??${weekNumber}?`;
    },

    // ???????
    formatMonthDate(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');

      // 返回格式化的月份第一天
      return `${year}年${month}月1日`;
    },

    // 自定义天数变化处理
    onCustomDaysChange(e) {
      this.planForm.customDays = e.detail.value;
    },

    // 获取结束日期显示
    getEndDateDisplay() {
      if (!this.planForm.date) {
        return '请选择开始日期';
      }

      const startDate = new Date(this.planForm.date);
      let endDate = new Date(startDate);

      // 根据计划类型计算结束日期
      if (this.planForm.type === 'daily') {
        // 日计划，结束日期就是开始日期
        return this.formatDate(endDate);
      } else if (this.planForm.type === 'weekly') {
        // 周计划，结束日期是开始日期加6天
        endDate.setDate(startDate.getDate() + 6);
        return this.formatDate(endDate);
      } else if (this.planForm.type === 'monthly') {
        // 月计划，结束日期是当月最后一天
        endDate.setMonth(startDate.getMonth() + 1);
        endDate.setDate(0); // 设置为上月最后一天
        return this.formatDate(endDate);
      } else if (this.planForm.type === 'custom') {
        // 自定义计划，根据自定义天数计算结束日期
        endDate.setDate(startDate.getDate() + this.planForm.customDays - 1);
        return this.formatDate(endDate);
      }

      return '未知';
    },

    // 子任务时间变化处理
    onSubTaskStartTimeChange(e) {
      this.newSubTask.startTime = e.detail.value;
    },

    onSubTaskEndTimeChange(e) {
      this.newSubTask.endTime = e.detail.value;
    },

    addSubTask() {
      // 验证标题
      if (!this.newSubTask.title) {
        uni.showToast({
          title: '请输入子任务标题',
          icon: 'none'
        });
        return;
      }

      // 验证时间
      if (!this.newSubTask.startTime || !this.newSubTask.endTime) {
        uni.showToast({
          title: '请选择开始和结束时间',
          icon: 'none'
        });
        return;
      }

      // 验证时间逻辑
      const startMinutes = this.timeToMinutes(this.newSubTask.startTime);
      const endMinutes = this.timeToMinutes(this.newSubTask.endTime);
      if (startMinutes >= endMinutes) {
        uni.showToast({
          title: '结束时间必须晚于开始时间',
          icon: 'none'
        });
        return;
      }

      // 生成新的子任务ID
      const newId = this.planForm.subTasks.length > 0
        ? Math.max(...this.planForm.subTasks.map(t => t.id || 0)) + 1
        : 1;

      // 添加子任务
      this.planForm.subTasks.push({
        id: newId,
        title: this.newSubTask.title,
        startTime: this.newSubTask.startTime,
        endTime: this.newSubTask.endTime,
        description: this.newSubTask.description,
        completed: false
      });

      // 重置表单
      this.newSubTask = {
        title: '',
        startTime: '09:00',
        endTime: '10:00',
        description: '',
        completed: false
      };

      // 隐藏表单
      this.showAddSubTaskForm = false;

      // 提示成功
      uni.showToast({
        title: '子任务添加成功',
        icon: 'success'
      });

      console.log('子任务列表', this.planForm.subTasks);
    },

    cancelAddSubTask() {
      // 重置子任务表单并隐藏
      this.newSubTask = {
        title: '',
        startTime: '09:00',
        endTime: '10:00',
        description: '',
        completed: false
      };
      this.showAddSubTaskForm = false;
    },

    deleteSubTask(index) {
      if (this.planForm.subTasks && this.planForm.subTasks[index]) {
        uni.showModal({
          title: '确认删除',
          content: '确定要删除这个子任务吗？',
          success: (res) => {
            if (res.confirm) {
              this.planForm.subTasks.splice(index, 1);
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          }
        });
      }
    },

    // 时间转换为分钟数
    timeToMinutes(timeStr) {
      if (!timeStr) return 0;

      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    },

    // 根据任务ID获取原始索引位置
    getOriginalIndex(id) {
      return this.planForm.subTasks.findIndex(task => task.id === id);
    },

    savePlan() {
      try {
        // 表单验证
        if (!this.planForm.title) {
          uni.showToast({
            title: '请输入计划标题',
            icon: 'none'
          });
          return;
        }

        if (!this.planForm.date) {
          uni.showToast({
            title: '请选择日期',
            icon: 'none'
          });
          return;
        }

        // 显示加载状态
        uni.showLoading({
          title: '保存中...',
          mask: true
        });

        // 构建计划数据
        const planData = {
          title: this.planForm.title,
          type: this.planForm.type,
          date: this.planForm.date,
          priority: this.planForm.priority || 'medium',
          description: this.planForm.description || '',
          completed: false
        };

        // 计算结束日期
        const startDate = new Date(this.planForm.date);
        let endDate = new Date(startDate);

        if (this.planForm.type === 'daily') {
          // 日计划，结束日期就是开始日期
          planData.endDate = this.planForm.date;
        } else if (this.planForm.type === 'weekly') {
          // 周计划，结束日期是开始日期加6天
          endDate.setDate(startDate.getDate() + 6);
          planData.endDate = endDate.toISOString().split('T')[0];
        } else if (this.planForm.type === 'monthly') {
          // 月计划，结束日期是当月最后一天
          endDate.setMonth(startDate.getMonth() + 1);
          endDate.setDate(0); // 设置为上月最后一天
          planData.endDate = endDate.toISOString().split('T')[0];
        } else if (this.planForm.type === 'custom') {
          // 自定义计划
          planData.customDays = this.planForm.customDays;
          endDate.setDate(startDate.getDate() + this.planForm.customDays - 1);
          planData.endDate = endDate.toISOString().split('T')[0];
        }

         if (this.planForm.subTasks && this.planForm.subTasks.length > 0) {
          planData.subTasks = this.planForm.subTasks.map(task => {
            return {
              title: task.title,
              startTime: task.startTime,
              endTime: task.endTime,
              description: task.description || '',
              completed: task.completed || false
            };
          });
        } else {
          planData.subTasks = [];
        }

        console.log('?????????????', planData);

        // ??API????
        planApi.createPlan(planData).then(res => {
          if (res.code === 200) {
            console.log('??????:', res.data);

            // ??????
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2]; // ??????
            // ???????????????
             if (prevPage && prevPage.$vm) {
              // ??????????
              prevPage.$vm.plans = [];

              // ??????????????????????
               if (typeof prevPage.$vm.loadPlans === 'function') {
                setTimeout(() => {
                  prevPage.$vm.loadPlans();
                }, 100);
              }

              // ????????
              if (typeof prevPage.$vm.updatePlanStats === 'function') {
                prevPage.$vm.updatePlanStats();
              }
            }

            // ??????
            uni.navigateBack({
              success: () => {
                // ????
                uni.showToast({
                  title: '??????',
                  icon: 'success'
                });
              }
            });
          } else {
            console.error('??????:', res.message);
            uni.showToast({
              title: res.message || '??????',
              icon: 'none'
            });
          }
        }).catch(err => {
          console.error('??????:', err);
          uni.showToast({
            title: '??????????',
            icon: 'none'
          });
        }).finally(() => {
          uni.hideLoading();
        });
      } catch (error) {
        console.error('savePlan ??????:', error);
        uni.hideLoading();
        uni.showToast({
          title: '????????',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style>
.add-plan-container {
  min-height: 800px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  background-image: linear-gradient(to bottom, #eef2ff, #f5f7fa);
}

.header {
  height: 60px;
  background: rgba(255, 255, 255, 0.85);

  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 12px rgba(31, 60, 136, 0.08);
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  transition: transform 0.2s ease;
}

.back-button:active {
  transform: scale(0.92);
}

.back-icon {
  font-size: 24px;
  color: #5B7FFF;
  font-weight: normal;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #2E3A59;
  letter-spacing: 0.5px;
}

.placeholder {
  width: 40px;
}

.form-container {
  flex: 1;
  padding: 20px;
  padding-bottom: 120px;
}

.form-item {
  margin-bottom: 20px;
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 6px 16px rgba(31, 60, 136, 0.06);
  position: relative;
  overflow: hidden;
}

.form-label {
  font-size: 15px;
  color: #2E3A59;
  margin-bottom: 12px;
  display: flex;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #2E3A59;
  background-color: #F9FAFC;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.form-input:focus {
  border-color: #5B7FFF;
  background-color: #FFFFFF;
  box-shadow: 0 0 0 3px rgba(91, 127, 255, 0.1);
}

.form-textarea {
  width: 100%;
  height: 120px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  color: #2E3A59;
  background-color: #F9FAFC;
}

.plan-type-selector {
  display: flex;
  flex-direction: row;
  background-color: #F5F7FA;
  border-radius: 12px;
  overflow: hidden;
  padding: 4px;
}

.plan-type-item {
  flex: 1;
  padding: 12px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.plan-type-item--active {
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.plan-type-item--disabled {
  opacity: 0.6;
  pointer-events: none;
}

.plan-type-item__text {
  font-size: 15px;
  font-weight: bold;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.plan-type-item--active .plan-type-item__text {
  color: #FFFFFF;
  font-weight: bold;
}

.priority-selector {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 8px;
}

.priority-item {
  flex: 1;
  margin: 0 4px;
  padding: 10px 0;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
  background-color: #F5F7FA;
}

.priority-item:first-child {
  margin-left: 0;
}

.priority-item:last-child {
  margin-right: 0;
}

.priority-item--active.priority-item--low {
  background: linear-gradient(135deg, #52C41A, #73D13D);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
}

.priority-item--active.priority-item--medium {
  background: linear-gradient(135deg, #FAAD14, #FFC53D);
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.15);
}

.priority-item--active.priority-item--high {
  background: linear-gradient(135deg, #FF4D4F, #FF7875);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.15);
}

.priority-item__text {
  font-size: 14px;
  color: #5E6C84;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: ease;
}

.priority-item--active .priority-item__text {
  color: #FFFFFF;
  font-weight: bold;
}

.picker-item {
  width: 100%;
  height: 48px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  padding: 0 16px;
  font-size: 16px;
  color: #2E3A59;
  background-color: #F9FAFC;
  display: flex;
  align-items: center;
}

/* ??????*/
.subtask-list {
  margin-top: 12px;
}

.subtask-list-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 6px 12px;
  background-color: rgba(240, 248, 255, 0.5);
  border-radius: 8px;
}

.subtask-list-title {
  font-size: 13px;
  color: #2E3A59;
  font-weight: bold;
}

.subtask-list-subtitle {
  font-size: 11px;
  color: #5B7FFF;
  background-color: #FFFFFF;
  padding: 2px 6px;
  border-radius: 10px;
}

.subtask-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px;
  background-color: #FFFFFF;
  border-radius: 12px;
  margin-bottom: 8px;
  box-shadow: 0 4px 10px rgba(31, 60, 136, 0.04);
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
  border: 1px solid rgba(240, 243, 250, 0.8);
  height: 64px;
}

.subtask-item:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(31, 60, 136, 0.03);
}

.subtask-checkbox {
  width: 20px;
  height: 20px;
  border-radius: 999px;
  border: 2px solid #80B8F5;
  margin-right: 12px;
  flex-shrink: 0;
  position: relative;
  transition-property: transform, opacity; transition-duration: 0.3s; transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.subtask-checkbox--checked {
  background-color: #4CAF50;
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.subtask-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0; /* ?????? */
}

.subtask-title {
  font-size: 15px;
  font-weight: bold;
  color: #2E3A59;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subtask-title--completed {
  color: #8F9BB3;
  text-decoration: line-through;
  font-weight: 400;
}

.subtask-time {
  font-size: 11px;
  color: #5E6C84;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.subtask-description {
  font-size: 11px;
  color: #8F9BB3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  margin-left: 4px;
}

.subtask-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.subtask-action {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.subtask-action--delete {
  background-color: rgba(255, 77, 79, 0.1);
}

.subtask-action--delete:active {
  transform: scale(0.9);
  background-color: rgba(255, 77, 79, 0.2);
}

.subtask-action__icon {
  font-size: 14px;
  color: #FF4D4F;
}

/* ??????*/
.subtask-form {
  margin-top: 12px;
  padding: 16px;
  background-color: rgba(91, 127, 255, 0.03);
  border-radius: 12px;
  border: 1px dashed #80B8F5;
}

.subtask-form-item {
  margin-bottom: 16px;
}

.subtask-form-actions {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 8px;
}

/* ????????*/
.section-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.add-subtask-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6px 10px;
  background-color: #F0F8FF;
  border-radius: 12px;
  transition-property: transform, opacity; transition-duration: 0.2s; transition-timing-function: ease;
}

.add-subtask-btn:active {
  transform: scale(0.96);
  background-color: #E0F0FF;
}

.add-subtask-icon {
  font-size: 14px;
  color: #5B7FFF;
  margin-right: 4px;
}

.add-subtask-text {
  font-size: 12px;
  color: #5B7FFF;
}

/* ??????*/
.empty-subtasks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  background-color: #F8F9FA;
  border-radius: 12px;
  border: 1px dashed #E0E0E0;
  margin-top: 12px;
}

.empty-subtasks__text {
  font-size: 14px;
  color: #8F9BB3;
  text-align: center;
  line-height: 1.5;
}

/* ??????*/
.time-range-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.picker-item {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  font-size: 15px;
  color: #2E3A59;
  background-color: #F9FAFC;
  margin: 0 4px;
}

.picker-item:first-child {
  margin-left: 0;
}

.picker-item:last-child {
  margin-right: 0;
}

.picker-separator {
  padding: 0 8px;
  color: #5E6C84;
}

/* ???? */
.footer {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.9);

  border-top: 1px solid rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.btn-cancel {
  flex: 1;
  padding: 12px 0;
  background-color: rgba(240, 243, 250, 0.8);
  border-radius: 12px;
  font-size: 15px;
  color: #5E6C84;
  margin-right: 10px;
  text-align: center;
  font-weight: bold;
}

.btn-primary {
  flex: 1;
  padding: 12px 0;
  background: linear-gradient(135deg, #5B7FFF, #80B8F5);
  border-radius: 12px;
  font-size: 15px;
  color: #FFFFFF;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(91, 127, 255, 0.15);
}

.btn-hover {
  opacity: 0.9;
  transform: scale(0.98);
}

.plan-type-hint {
  font-size: 12px;
  color: #8F9BB3;
  margin-top: 8px;
  line-height: 1.4;
  padding: 0 4px;
}

.end-date-display {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48px;
  background-color: #F9FAFC;
  border-radius: 12px;
  padding: 0 16px;
  border: 1px solid #E0E0E0;
}

.end-date-text {
  font-size: 16px;
  color: #2E3A59;
  text-align: center;
}
</style>


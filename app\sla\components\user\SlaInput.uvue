<template>
  <view class="sla-input" :class="{ 'sla-input--error': error, 'sla-input--disabled': disabled }">
    <view class="sla-input__label" v-if="label">
      <text class="sla-input__label-text">{{ label }}</text>
      <text class="sla-input__required" v-if="required">*</text>
    </view>
    <view class="sla-input__wrapper">
      <view class="sla-input__prefix" v-if="$slots.prefix">
        <slot name="prefix"></slot>
      </view>
      <input
        class="sla-input__field"
        :type="type"
        :value="value"
        :placeholder="placeholder"
        :password="type === 'password'"
        :disabled="disabled"
        :maxlength="maxlength"
        :focus="focus"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      <view class="sla-input__suffix" v-if="$slots.suffix || clearable || type === 'password'">
        <view class="sla-input__clear" v-if="clearable && value && !disabled" @click="handleClear">
          <text class="sla-input__clear-icon">Ă</text>
        </view>
        <view class="sla-input__password-toggle" v-if="type === 'password'" @click="togglePasswordVisible">
          <text class="sla-input__password-icon">đ</text>
        </view>
        <slot name="suffix"></slot>
      </view>
    </view>
    <view class="sla-input__error" v-if="error">
      <text class="sla-input__error-text">{{ error }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SlaInput',
  props: {
    // čžĺĽćĄĺ?    
    value: {
      type: String,
      default: ''
    },
    // čžĺĽćĄçąťĺ?    type: {
      type: String,
      default: 'text'
    },
    // čžĺĽćĄć ç­?    label: {
      type: String,
      default: ''
    },
    // čžĺĽćĄĺ ä˝çŹŚ
    placeholder: {
      type: String,
      default: 'čŻˇčžĺ?
    },
    // ćŻĺŚĺżĺĄŤ
    required: {
      type: Boolean,
      default: false
    },
    // ćŻĺŚçŚç¨
    disabled: {
      type: Boolean,
      default: false
    },
    // ćĺ¤§éżĺş?    maxlength: {
      type: Number,
      default: -1
    },
    // ćŻĺŚĺŻć¸é?    clearable: {
      type: Boolean,
      default: false
    },
    // éčŻŻäżĄćŻ
    error: {
      type: String,
      default: ''
    },
    // ćŻĺŚčŞĺ¨čçŚ
    focus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      passwordVisible: false
    };
  },
  computed: {
    // ĺŽéčžĺĽçąťĺ
    actualType() {
      if (this.type === 'password' && this.passwordVisible) {
        return 'text';
      }
      return this.type;
    }
  },
  methods: {
    // ĺ¤çčžĺĽäşäťś
    handleInput(e) {
      this.$emit('input', e.detail.value);
    },
    // ĺ¤çčçŚäşäťś
    handleFocus(e) {
      this.$emit('focus', e);
    },
    // ĺ¤çĺ¤ąçŚäşäťś
    handleBlur(e) {
      this.$emit('blur', e);
    },
    // ĺ¤çć¸é¤äşäťś
    handleClear() {
      this.$emit('input', '');
      this.$emit('clear');
    },
    // ĺć˘ĺŻç ĺŻč§ć?    togglePasswordVisible() {
      this.passwordVisible = !this.passwordVisible;
    }
  }
}
</script>

<style>
.sla-input {
  margin-bottom: 16px;
}

.sla-input__label {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.sla-input__label-text {
  font-size: 14px;
  color: #333333;
  font-weight: bold;
}

.sla-input__required {
  color: #DD524D;
  margin-left: 4px;
}

.sla-input__wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  background-color: #FFFFFF;
  padding: 0 12px;
  height: 44px;
}

.sla-input--error .sla-input__wrapper {
  border-color: #DD524D;
}

.sla-input--disabled .sla-input__wrapper {
  background-color: #F8F9FA;
  opacity: 0.7;
}

.sla-input__prefix {
  margin-right: 8px;
}

.sla-input__field {
  flex: 1;
  height: 100%;
  font-size: 16px;
  color: #333333;
}

.sla-input__suffix {
  margin-left: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.sla-input__clear, .sla-input__password-toggle {
  padding: 4px;
}

.sla-input__clear-icon {
  font-size: 18px;
  color: #999999;
}

.sla-input__password-icon {
  font-size: 18px;
  color: #999999;
}

.sla-input__error {
  margin-top: 4px;
}

.sla-input__error-text {
  font-size: 12px;
  color: #DD524D;
}
</style>

